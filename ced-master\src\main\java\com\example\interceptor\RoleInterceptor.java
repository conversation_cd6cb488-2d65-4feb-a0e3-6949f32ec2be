package com.example.interceptor;

import com.example.annotation.HasRole;
import com.example.config.LoginUserContext;
import com.example.enums.LogicalType;
import com.example.util.HttpStatus;
import com.example.util.R;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

// 创建角色检查拦截器
@Component
public class RoleInterceptor implements HandlerInterceptor {
    @Resource
    private ObjectMapper objectMapper;

    /**
     * 在 Controller 方法执行前 调用，用于权限校验、日志记录等预处理操作。
     * @param request:封装请求信息（如请求头、参数、Session）
     * @param response:用于直接返回响应（如拦截时返回错误信息）。
     * @param handler:当前请求对应的处理器（如 Controller 方法或 HandlerMethod 对象）。
     * @return
     * @throws Exception
     */
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if (!(handler instanceof HandlerMethod)) {
            return true;
        }

        HandlerMethod handlerMethod = (HandlerMethod) handler;
        HasRole hasRole = handlerMethod.getMethodAnnotation(HasRole.class);
        
        if (hasRole == null) {
            return true;
        }

        // 获取当前登录用户ID
        Long userId = LoginUserContext.getUserInfo().getUserId();
        if (userId == null) {
            response.getOutputStream().write(
                    objectMapper.writeValueAsString(R.fail(HttpStatus.UNAUTHORIZED,"用户不存在,请重新登录")).getBytes(StandardCharsets.UTF_8)
            );
            return false;
        }

        // 获取用户所有角色
        Set<String> userRoles = new HashSet<>();
        
        // 获取注解中的角色列表
        String[] requireRoles = hasRole.value().split(",");
        
        // 根据验证模式进行角色验证
        boolean hasPermission;
        if (hasRole.logical() == LogicalType.ANY) {
            // 任意一个角色即可
            hasPermission = Arrays.stream(requireRoles)
                    .map(String::trim)
                    .anyMatch(userRoles::contains);
        } else {
            // 需要具有所有角色
            hasPermission = Arrays.stream(requireRoles)
                    .map(String::trim)
                    .allMatch(userRoles::contains);
        }

        if (!hasPermission) {
            response.getOutputStream().write(
                    objectMapper.writeValueAsString(R.fail(HttpStatus.UNAUTHORIZED,"该用户没有权限")).getBytes(StandardCharsets.UTF_8)
            );
            return false;
        }

        return true;
    }
}