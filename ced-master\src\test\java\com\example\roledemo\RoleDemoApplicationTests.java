package com.example.roledemo;

import com.example.entity.SysUser;
import com.example.enums.Role;
import com.example.mapper.UserMapper;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

@SpringBootTest
class RoleDemoApplicationTests {
    @Resource
    private UserMapper userMapper;
    @Test
    void contextLoads() {
        SysUser user = userMapper.selectById(8L);
        System.out.println(user);
        /*SysUser user1 = new SysUser();
        user1.setUserId(8L);
        user1.setUsername("11");
        user1.setPassword("123456");
        user1.setRole(Role.ADMIN);
        userMapper.insert(user1);*/
    }

}
