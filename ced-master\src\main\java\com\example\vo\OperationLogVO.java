package com.example.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 操作日志视图对象
 */
@Data
public class OperationLogVO {
    private Long logId;
    private String operatorName; // 操作员姓名
    private String operatorType; // 操作员类型文本
    private String actionType; // 操作类型
    private String actionName; // 具体操作名称
    private String targetType; // 操作对象类型
    private String targetName; // 操作对象名称
    private String description; // 操作描述
    private String requestMethod; // 请求方法
    private String ipAddress; // 操作IP地址
    private String status; // 操作状态文本
    private String errorMessage; // 错误信息
    private Long executionTime; // 执行时间（毫秒）
    private LocalDateTime createTime; // 操作时间
    
    // 格式化后的字段
    private String time; // 格式化的时间
    private String action; // 操作类型（用于前端显示）
    private String target; // 操作对象（用于前端显示）
    private String result; // 结果（成功/失败）
}
