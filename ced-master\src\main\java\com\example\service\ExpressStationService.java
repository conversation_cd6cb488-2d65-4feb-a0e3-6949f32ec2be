package com.example.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.example.entity.ExpressStation;
import com.example.mapper.ExpressStationMapper;
import com.example.util.R;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ExpressStationService {
    
    @Autowired
    private ExpressStationMapper expressStationMapper;
    
    /**
     * 获取所有可用的快递站点
     */
    public R<List<ExpressStation>> getAllStations() {
        LambdaQueryWrapper<ExpressStation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ExpressStation::getStatus, 1)
                   .orderByAsc(ExpressStation::getCampus)
                   .orderByAsc(ExpressStation::getStationName);
        
        List<ExpressStation> stations = expressStationMapper.selectList(queryWrapper);
        return R.ok(stations);
    }
    
    /**
     * 根据校区获取快递站点
     */
    public R<List<ExpressStation>> getStationsByCampus(String campus) {
        LambdaQueryWrapper<ExpressStation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ExpressStation::getCampus, campus)
                   .eq(ExpressStation::getStatus, 1)
                   .orderByAsc(ExpressStation::getStationName);
        
        List<ExpressStation> stations = expressStationMapper.selectList(queryWrapper);
        return R.ok(stations);
    }
    
    /**
     * 获取站点详情
     */
    public R<ExpressStation> getStationDetail(Long stationId) {
        ExpressStation station = expressStationMapper.selectById(stationId);
        if (station == null) {
            return R.fail("站点不存在");
        }
        return R.ok(station);
    }
}