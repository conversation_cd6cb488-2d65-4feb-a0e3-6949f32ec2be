package com.example.aspect;

import com.example.annotation.OperationLog;
import com.example.service.OperationLogService;
import com.example.util.JwtUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.Arrays;

/**
 * 操作日志切面
 */
@Slf4j
@Aspect
@Component
public class OperationLogAspect {
    
    @Autowired
    private OperationLogService operationLogService;
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    @Pointcut("@annotation(com.example.annotation.OperationLog)")
    public void operationLogPointcut() {}
    
    @Around("operationLogPointcut()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();
        
        // 获取注解信息
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        OperationLog operationLog = method.getAnnotation(OperationLog.class);
        
        // 获取请求信息
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes != null ? attributes.getRequest() : null;
        
        // 获取用户信息
        Long operatorId = null;
        String operatorName = "系统";
        Integer operatorType = 1; // 默认管理员
        
        if (request != null) {
            String token = request.getHeader("Authorization");
            if (token != null && token.startsWith("Bearer ")) {
                token = token.substring(7);
                try {
                    operatorId = JwtUtil.getUserIdFromToken(token);
                    operatorName = JwtUtil.getUsernameFromToken(token);
                    // 这里可以根据需要获取用户类型
                } catch (Exception e) {
                    log.warn("解析token失败", e);
                }
            }
        }
        
        String requestUrl = request != null ? request.getRequestURI() : "";
        String requestMethod = request != null ? request.getMethod() : "";
        String requestParams = "";
        
        // 记录请求参数
        if (operationLog.recordParams()) {
            try {
                Object[] args = joinPoint.getArgs();
                if (args != null && args.length > 0) {
                    // 过滤敏感参数
                    Object[] filteredArgs = Arrays.stream(args)
                            .map(this::filterSensitiveData)
                            .toArray();
                    requestParams = objectMapper.writeValueAsString(filteredArgs);
                }
            } catch (Exception e) {
                log.warn("序列化请求参数失败", e);
                requestParams = "参数序列化失败";
            }
        }
        
        Object result = null;
        String errorMessage = null;
        Integer status = 1; // 成功
        
        try {
            // 执行目标方法
            result = joinPoint.proceed();
            return result;
        } catch (Exception e) {
            status = 0; // 失败
            errorMessage = e.getMessage();
            throw e;
        } finally {
            long executionTime = System.currentTimeMillis() - startTime;
            
            // 记录响应结果
            String responseResult = "";
            if (operationLog.recordResult() && result != null) {
                try {
                    responseResult = objectMapper.writeValueAsString(result);
                } catch (Exception e) {
                    log.warn("序列化响应结果失败", e);
                    responseResult = "结果序列化失败";
                }
            }
            
            // 异步记录日志
            try {
                operationLogService.recordLog(
                        operatorId,
                        operatorName,
                        operatorType,
                        operationLog.actionType(),
                        operationLog.actionName(),
                        operationLog.targetType(),
                        null, // targetId 需要在具体方法中设置
                        null, // targetName 需要在具体方法中设置
                        operationLog.description(),
                        requestMethod,
                        requestUrl,
                        requestParams,
                        responseResult,
                        status,
                        errorMessage,
                        executionTime
                );
            } catch (Exception e) {
                log.error("记录操作日志失败", e);
            }
        }
    }
    
    /**
     * 过滤敏感数据
     */
    private Object filterSensitiveData(Object obj) {
        if (obj == null) {
            return null;
        }
        
        String objStr = obj.toString();
        // 过滤密码等敏感信息
        if (objStr.contains("password") || objStr.contains("pwd")) {
            return "***敏感信息已过滤***";
        }
        
        return obj;
    }
}
