# 校园快递代取系统

一个基于Spring Boot的校园快递代取服务平台，为学生提供便捷的快递代取服务。

## 🎯 项目特色

- **三端分离**：用户端、代取员端、管理员端功能完整分离
- **完整流程**：从下单到完成的完整订单流程管理
- **实时状态**：订单状态实时更新，支持多种状态跟踪
- **安全可靠**：用户身份认证、代取员审核机制
- **易于扩展**：模块化设计，便于功能扩展

## 🏗️ 技术架构

- **后端框架**：Spring Boot 2.7.2
- **数据库**：MySQL 8.0
- **ORM框架**：MyBatis Plus 3.0.5
- **缓存**：Redis
- **认证**：JWT Token
- **文档**：接口文档自动生成

## 📋 核心功能

### 用户端功能
- ✅ 用户注册与登录（学号、手机号验证）
- ✅ 地址管理（支持多地址、默认地址设置）
- ✅ 下单功能（选择快递站点、填写取件码、设置代取费用）
- ✅ 订单管理（查看订单状态、取消订单、确认收货）
- ✅ 支付功能（定金支付、尾款支付）
- 🔄 评价系统（对代取员服务评分）
- 🔄 个人中心（钱包管理、消费记录）

### 代取员端功能
- ✅ 代取员注册与认证（实名认证、学生身份验证）
- ✅ 订单大厅（查看待接订单、抢单功能）
- ✅ 订单管理（已接订单、状态更新）
- ✅ 订单流程（取件确认、配送确认、送达确认）
- 🔄 收入管理（收益查看、提现功能）
- 🔄 个人中心（评分查看、接单设置）

### 管理员端功能
- ✅ 用户管理（查看、审核、禁用用户）
- ✅ 代取员管理（审核代取员资质、管理状态）
- ✅ 订单管理（查看所有订单、处理异常订单）
- ✅ 快递站点管理（添加、编辑、删除站点）
- ✅ 数据统计（用户数量、订单统计、收入统计）
- 🔄 财务管理（平台流水、抽成管理）
- 🔄 系统设置（参数配置、通知模板）

## 🗄️ 数据库设计

### 核心数据表
- `user` - 用户表（支持多角色：普通用户、代取员、管理员）
- `address` - 用户地址表
- `express_station` - 快递站点表
- `order_info` - 订单表
- `order_evaluation` - 订单评价表
- `wallet_record` - 钱包流水记录表
- `system_notice` - 系统通知表

## 🚀 快速开始

### 环境要求
- JDK 1.8+
- MySQL 8.0+
- Redis 6.0+
- Maven 3.6+

### 安装步骤

1. **克隆项目**
   ```bash
   git clone [项目地址]
   cd campus-express-delivery
   ```

2. **创建数据库**
   ```sql
   CREATE DATABASE express_delivery CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   ```

3. **导入数据**
   ```bash
   mysql -u root -p express_delivery < src/main/resources/sql/express_delivery.sql
   ```

4. **修改配置**
   编辑 `src/main/resources/application.yml`，修改数据库连接信息：
   ```yaml
   spring:
     datasource:
       url: ********************************************
       username: your_username
       password: your_password
   ```

5. **启动应用**
   ```bash
   mvn spring-boot:run
   ```

6. **访问应用**
   - 应用地址：http://localhost:18080/api
   - 系统首页：http://localhost:18080/api/index.html

## 🔗 API接口

### 用户相关
- `POST /api/user/register` - 用户注册
- `POST /api/user/login` - 用户登录
- `GET /api/user/profile` - 获取用户信息

### 订单相关
- `POST /api/order/create` - 创建订单
- `POST /api/order/accept/{orderId}` - 接单
- `GET /api/order/my-orders` - 查看我的订单
- `GET /api/order/pending` - 查看待接单列表
- `POST /api/order/confirm-pickup/{orderId}` - 确认取件
- `POST /api/order/confirm-delivery/{orderId}` - 确认送达
- `POST /api/order/confirm-receive/{orderId}` - 确认收货

### 快递相关
- `GET /api/express/stations` - 获取快递站点列表
- `GET /api/express/addresses` - 获取用户地址列表
- `POST /api/express/addresses` - 添加地址

### 管理员相关
- `GET /api/admin/statistics` - 获取系统统计
- `GET /api/admin/pickup-users/pending` - 待审核代取员
- `POST /api/admin/pickup-users/{userId}/audit` - 审核代取员

## 👥 测试账号

| 角色 | 用户名 | 密码 | 说明 |
|------|--------|------|------|
| 管理员 | admin | 123456 | 系统管理员账号 |
| 普通用户 | student001 | 123456 | 测试用户账号 |
| 代取员 | pickup001 | 123456 | 测试代取员账号 |

## 📊 订单状态流转

```
待接单(1) → 已接单(2) → 取件中(3) → 配送中(4) → 待确认(5) → 已完成(6)
    ↓
  已取消(7)
```

## 💰 支付状态流转

```
待支付定金(1) → 已支付定金(2) → 待支付尾款(3) → 已支付完成(4)
```

## 🔄 后续开发计划

- [ ] 支付系统集成（支付宝、微信支付）
- [ ] 消息推送系统（短信、邮件、站内信）
- [ ] 评价系统完善
- [ ] 钱包系统完善
- [ ] 数据统计报表
- [ ] 移动端适配
- [ ] 小程序版本

## 🤝 贡献指南

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系方式

如有问题或建议，请通过以下方式联系：

- 项目Issues：[GitHub Issues](项目Issues地址)
- 邮箱：[<EMAIL>]

---

⭐ 如果这个项目对你有帮助，请给个Star支持一下！