package com.example.service;

import com.example.dto.OrderCreateDTO;
import com.example.entity.Order;
import com.example.vo.OrderVO;
import com.example.util.R;
import java.util.List;

/**
 * 订单服务接口
 */
public interface OrderService {
    
    /**
     * 创建订单
     */
    R<String> createOrder(OrderCreateDTO orderCreateDTO);
    
    /**
     * 接单
     */
    R<String> acceptOrder(Long orderId);
    
    /**
     * 取消订单
     */
    R<String> cancelOrder(Long orderId, String reason);
    
    /**
     * 确认取件
     */
    R<String> confirmPickup(Long orderId);
    
    /**
     * 确认送达
     */
    R<String> confirmDelivery(Long orderId);
    
    /**
     * 用户确认收货
     */
    R<String> confirmReceive(Long orderId);
    
    /**
     * 查询用户订单列表
     */
    R<List<OrderVO>> getUserOrders(Long userId);
    
    /**
     * 查询代取员订单列表
     */
    R<List<OrderVO>> getPickupUserOrders(Long pickupUserId);
    
    /**
     * 查询待接单列表
     */
    R<List<OrderVO>> getPendingOrders();
    
    /**
     * 查询订单详情
     */
    R<OrderVO> getOrderDetail(Long orderId);
}