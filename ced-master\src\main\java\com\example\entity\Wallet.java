package com.example.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 钱包流水记录实体类
 */
@Data
@TableName("wallet_record")
public class Wallet {
    @TableId(type = IdType.AUTO)
    @TableField("record_id")
    private Long recordId;
    
    @TableField("user_id")
    private Long userId;
    
    @TableField("order_id")
    private Long orderId;
    
    @TableField("transaction_type")
    private Integer transactionType; // 交易类型：1-充值，2-消费，3-收入，4-提现，5-退款
    
    private BigDecimal amount; // 交易金额
    
    @TableField("balance_before")
    private BigDecimal balanceBefore; // 交易前余额
    
    @TableField("balance_after")
    private BigDecimal balanceAfter; // 交易后余额
    
    private String description; // 交易描述
    
    @TableField("transaction_no")
    private String transactionNo; // 交易流水号
    
    private Integer status; // 状态：1-成功，2-处理中，3-失败
    
    @TableField("create_time")
    private LocalDateTime createTime;
}