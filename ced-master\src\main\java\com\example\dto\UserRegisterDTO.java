package com.example.dto;

import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * 用户注册DTO
 */
@Data
public class UserRegisterDTO {
    
    @NotBlank(message = "用户名不能为空")
    private String username;
    
    @NotBlank(message = "密码不能为空")
    private String password;
    
    @NotBlank(message = "手机号不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;
    
    @NotBlank(message = "学号不能为空")
    private String studentId;
    
    @NotBlank(message = "真实姓名不能为空")
    private String realName;
    
    private String email;
    
    // 用户类型：1-普通用户，2-代取员
    private Integer userType = 1;
    
    // 代取员注册时需要的额外信息
    private String idCard;
    private String alipayAccount;
    private String wechatAccount;
}