package com.example.constant;

public interface Constants {

    public static final String LOGIN_INFO = "LOGIN_INFO";

    public static final String CAPTCHA = "CAPTCHA";
    /**
     * 验证码 redis key
     */
    public static final String CAPTCHA_CODE_KEY = "captcha_codes:";
    /**
     * 验证码有效期（分钟）
     */
    public static final Integer CAPTCHA_EXPIRATION = 2;
    /**
     * 用户名长度限制
     */
    public static final int USERNAME_MIN_LENGTH = 2;
    public static final int USERNAME_MAX_LENGTH = 20;

    /**
     * 密码长度限制
     */
    public static final int PASSWORD_MIN_LENGTH = 5;
    public static final int PASSWORD_MAX_LENGTH = 20;

    /**
     * 用户权限标识
     */
    public final static String ADMIN = "admin";
    public final static String MERCHANT = "merchant";
    public final static String COMMON = "common";

    /**
     * 账号状态：'active','pending','disabled'
     */
    public final static String ACCOUNT_ACTIVE = "active";
    public final static String ACCOUNT_PENDING = "pending";
    public final static String ACCOUNT_DISABLED = "disabled";
}
