package com.example.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.entity.ExpressStation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import java.util.List;

@Mapper
public interface ExpressStationMapper extends BaseMapper<ExpressStation> {
    
    /**
     * 查询可用的快递站点
     */
    @Select("SELECT * FROM express_station WHERE status = 1 ORDER BY station_name")
    List<ExpressStation> findAvailableStations();
    
    /**
     * 根据校区查询快递站点
     */
    @Select("SELECT * FROM express_station WHERE campus = #{campus} AND status = 1")
    List<ExpressStation> findByCampus(@Param("campus") String campus);
}