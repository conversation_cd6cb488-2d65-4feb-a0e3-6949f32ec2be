package com.example.vo;

import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 用户详细资料VO
 */
@Data
public class UserProfileVO {
    
    private Long userId;
    private String username;
    private String realName;
    private String phone;
    private String studentId;
    private String idCard;
    private String email;
    private String avatar;
    private Integer userType;
    private String userTypeName;
    private Integer status;
    private Integer auditStatus;
    private String auditStatusName;
    private String alipayAccount;
    private String wechatAccount;
    private BigDecimal balance;
    private BigDecimal rating;
    private Integer orderCount;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
    
    // 统计信息
    private Integer totalOrders;        // 总订单数
    private Integer completedOrders;    // 已完成订单数
    private Integer cancelledOrders;    // 已取消订单数
    private BigDecimal totalEarnings;   // 总收益（代取员）
    private BigDecimal totalSpent;      // 总消费（学生）
}