package com.example.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 通知视图对象
 */
@Data
public class NotificationVO {
    
    private Long noticeId; // 通知ID
    private String title; // 标题
    private String content; // 内容
    private Integer noticeType; // 通知类型
    private String noticeTypeText; // 通知类型文本
    private Integer targetType; // 目标类型
    private String targetTypeText; // 目标类型文本
    private Long targetUserId; // 目标用户ID
    private Integer status; // 状态
    private String statusText; // 状态文本
    private LocalDateTime publishTime; // 发布时间
    private LocalDateTime createTime; // 创建时间
    private Boolean isRead; // 是否已读
    private LocalDateTime readTime; // 读取时间
    
    /**
     * 获取通知类型文本
     */
    public String getNoticeTypeText() {
        if (noticeType == null) return "未知";
        switch (noticeType) {
            case 1: return "系统公告";
            case 2: return "订单通知";
            case 3: return "账户通知";
            case 4: return "安全通知";
            case 5: return "功能更新";
            default: return "其他";
        }
    }
    
    /**
     * 获取目标类型文本
     */
    public String getTargetTypeText() {
        if (targetType == null) return "未知";
        switch (targetType) {
            case 1: return "全部用户";
            case 2: return "学生";
            case 3: return "代取员";
            default: return "其他";
        }
    }
    
    /**
     * 获取状态文本
     */
    public String getStatusText() {
        if (status == null) return "未知";
        return status == 1 ? "已发布" : "草稿";
    }
}
