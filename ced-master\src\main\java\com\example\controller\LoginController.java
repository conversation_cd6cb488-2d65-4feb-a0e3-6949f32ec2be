package com.example.controller;

import cn.hutool.captcha.CaptchaUtil;
import cn.hutool.captcha.LineCaptcha;
import com.example.constant.Constants;
import com.example.dto.LoginDTO;
import com.example.dto.RegisterDTO;
import com.example.entity.SysUser;
import com.example.service.SysUserService;
import com.example.util.*;
import com.example.vo.UserInfoVo;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.concurrent.TimeUnit;

@RestController
public class LoginController {

    @Resource
    private RedisCache redisCache;
    @Resource
    private SysUserService userService;

    @PostMapping("/login")
    @Transactional(rollbackFor = Exception.class)
    public R<?> login(@Validated @RequestBody LoginDTO loginDTO) {
        userService.validateCaptcha(loginDTO.getCode(), loginDTO.getUuid());
        SysUser user = userService.checkUser(loginDTO.getUsername(), loginDTO.getPassword());
        userService.updateLoginInfo(user);
        
        UserInfoVo userInfo = new UserInfoVo();
        userInfo.setUserId(user.getUserId());
        userInfo.setUsername(user.getUsername());
        userInfo.setRealName(user.getRealName());
        userInfo.setUserType(user.getUserType().getValue());
        
        String token = JwtUtil.generateToken(userInfo);
        HashMap<String, Object> map = new HashMap<>();
        map.put("token", token);
        map.put("userInfo", userInfo);
        return R.ok(map);
    }

    @GetMapping("/roleList")
    public R<?> roleList() {
        return R.ok();
    }

    @PostMapping("/register")
    @Transactional
    public R<?> register(@Validated @RequestBody RegisterDTO registerDTO)
    {
        userService.registerUser(registerDTO);
        return R.ok();
    }

    @GetMapping("/captcha")
    public R<?> getCaptcha(){
        // 保存验证码信息
        String uuid = IdUtils.simpleUUID();
        String verifyKey = Constants.CAPTCHA_CODE_KEY + uuid;
        LineCaptcha captcha = CaptchaUtil.createLineCaptcha(80, 40, 4, 2);
        redisCache.setCacheObject(verifyKey, captcha.getCode(), Constants.CAPTCHA_EXPIRATION, TimeUnit.MINUTES);
        HashMap<String, String> map = new HashMap<>();
        map.put("uuid", uuid);
        map.put("img", "data:image/png;base64,"+captcha.getImageBase64());
        return R.ok(map);
    }

    @PostMapping("/logout")
    public R<?> logout() {
        return R.ok();
    }

}
