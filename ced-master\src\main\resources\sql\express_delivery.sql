-- 校园快递代取系统数据库初始化脚本

-- 用户表
CREATE TABLE `user` (
  `user_id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(100) NOT NULL COMMENT '密码',
  `phone` varchar(20) NOT NULL COMMENT '手机号',
  `student_id` varchar(20) NOT NULL COMMENT '学号',
  `real_name` varchar(50) NOT NULL COMMENT '真实姓名',
  `id_card` varchar(20) DEFAULT NULL COMMENT '身份证号',
  `avatar` varchar(200) DEFAULT NULL COMMENT '头像',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `user_type` int NOT NULL DEFAULT '1' COMMENT '用户类型：1-普通用户，2-代取员，3-管理员',
  `status` int NOT NULL DEFAULT '1' COMMENT '用户状态：1-正常，0-禁用',
  `alipay_account` varchar(100) DEFAULT NULL COMMENT '支付宝账号',
  `wechat_account` varchar(100) DEFAULT NULL COMMENT '微信账号',
  `audit_status` int DEFAULT '0' COMMENT '审核状态：0-待审核，1-审核通过，2-审核拒绝',
  `balance` decimal(10,2) DEFAULT '0.00' COMMENT '余额',
  `rating` decimal(3,2) DEFAULT '5.00' COMMENT '评分',
  `order_count` int DEFAULT '0' COMMENT '完成订单数',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`user_id`),
  UNIQUE KEY `uk_username` (`username`),
  UNIQUE KEY `uk_phone` (`phone`),
  UNIQUE KEY `uk_student_id` (`student_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- 地址表
CREATE TABLE `address` (
  `address_id` bigint NOT NULL AUTO_INCREMENT COMMENT '地址ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `campus` varchar(50) NOT NULL COMMENT '校区',
  `building` varchar(50) NOT NULL COMMENT '楼栋',
  `room` varchar(20) NOT NULL COMMENT '房间号',
  `detail_address` varchar(200) DEFAULT NULL COMMENT '详细地址',
  `contact_name` varchar(50) NOT NULL COMMENT '联系人姓名',
  `contact_phone` varchar(20) NOT NULL COMMENT '联系电话',
  `is_default` int NOT NULL DEFAULT '0' COMMENT '是否默认地址：1-是，0-否',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`address_id`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='地址表';

-- 快递站点表
CREATE TABLE `express_station` (
  `station_id` bigint NOT NULL AUTO_INCREMENT COMMENT '站点ID',
  `station_name` varchar(100) NOT NULL COMMENT '站点名称',
  `station_type` varchar(50) NOT NULL COMMENT '站点类型',
  `campus` varchar(50) NOT NULL COMMENT '所属校区',
  `location` varchar(200) NOT NULL COMMENT '具体位置',
  `open_time` varchar(100) DEFAULT NULL COMMENT '营业时间',
  `contact_phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
  `status` int NOT NULL DEFAULT '1' COMMENT '状态：1-正常营业，0-暂停服务',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`station_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='快递站点表';

-- 订单表
CREATE TABLE `order_info` (
  `order_id` bigint NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `order_no` varchar(50) NOT NULL COMMENT '订单号',
  `user_id` bigint NOT NULL COMMENT '下单用户ID',
  `pickup_user_id` bigint DEFAULT NULL COMMENT '代取员ID',
  `station_id` bigint NOT NULL COMMENT '快递站点ID',
  `pickup_code` varchar(50) NOT NULL COMMENT '取件码',
  `tracking_number` varchar(100) DEFAULT NULL COMMENT '运单号',
  `express_description` varchar(500) DEFAULT NULL COMMENT '快递描述',
  `address_id` bigint NOT NULL COMMENT '收货地址ID',
  `delivery_address` varchar(500) NOT NULL COMMENT '送达地址详情',
  `contact_name` varchar(50) NOT NULL COMMENT '联系人',
  `contact_phone` varchar(20) NOT NULL COMMENT '联系电话',
  `expected_time` datetime DEFAULT NULL COMMENT '期望送达时间',
  `pickup_time` datetime DEFAULT NULL COMMENT '取件时间',
  `delivery_time` datetime DEFAULT NULL COMMENT '送达时间',
  `service_fee` decimal(10,2) NOT NULL COMMENT '代取费用',
  `deposit` decimal(10,2) NOT NULL COMMENT '定金',
  `total_amount` decimal(10,2) NOT NULL COMMENT '总金额',
  `order_status` int NOT NULL DEFAULT '1' COMMENT '订单状态：1-待接单，2-已接单，3-取件中，4-配送中，5-待确认，6-已完成，7-已取消',
  `pay_status` int NOT NULL DEFAULT '1' COMMENT '支付状态：1-待支付定金，2-已支付定金，3-待支付尾款，4-已支付完成',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `cancel_reason` varchar(200) DEFAULT NULL COMMENT '取消原因',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`order_id`),
  UNIQUE KEY `uk_order_no` (`order_no`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_pickup_user_id` (`pickup_user_id`),
  KEY `idx_order_status` (`order_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单表';

-- 订单评价表
CREATE TABLE `order_evaluation` (
  `evaluation_id` bigint NOT NULL AUTO_INCREMENT COMMENT '评价ID',
  `order_id` bigint NOT NULL COMMENT '订单ID',
  `user_id` bigint NOT NULL COMMENT '评价用户ID',
  `pickup_user_id` bigint NOT NULL COMMENT '被评价代取员ID',
  `rating` int NOT NULL COMMENT '评分：1-5星',
  `comment` varchar(500) DEFAULT NULL COMMENT '评价内容',
  `images` varchar(1000) DEFAULT NULL COMMENT '评价图片',
  `evaluation_type` int NOT NULL DEFAULT '3' COMMENT '评价类型：1-服务态度，2-配送速度，3-整体满意度',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`evaluation_id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_pickup_user_id` (`pickup_user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单评价表';

-- 钱包流水记录表
CREATE TABLE `wallet_record` (
  `record_id` bigint NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `order_id` bigint DEFAULT NULL COMMENT '关联订单ID',
  `transaction_type` int NOT NULL COMMENT '交易类型：1-充值，2-消费，3-收入，4-提现，5-退款',
  `amount` decimal(10,2) NOT NULL COMMENT '交易金额',
  `balance_before` decimal(10,2) NOT NULL COMMENT '交易前余额',
  `balance_after` decimal(10,2) NOT NULL COMMENT '交易后余额',
  `description` varchar(200) NOT NULL COMMENT '交易描述',
  `transaction_no` varchar(50) NOT NULL COMMENT '交易流水号',
  `status` int NOT NULL DEFAULT '1' COMMENT '状态：1-成功，2-处理中，3-失败',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`record_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_transaction_no` (`transaction_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='钱包流水记录表';

-- 系统通知表
CREATE TABLE `system_notice` (
  `notice_id` bigint NOT NULL AUTO_INCREMENT COMMENT '通知ID',
  `title` varchar(200) NOT NULL COMMENT '通知标题',
  `content` text NOT NULL COMMENT '通知内容',
  `notice_type` int NOT NULL COMMENT '通知类型：1-系统公告，2-订单通知，3-账户通知',
  `target_type` int NOT NULL COMMENT '接收对象：1-全部用户，2-普通用户，3-代取员',
  `target_user_id` bigint DEFAULT NULL COMMENT '特定用户ID',
  `status` int NOT NULL DEFAULT '1' COMMENT '状态：1-已发布，0-草稿',
  `publish_time` datetime DEFAULT NULL COMMENT '发布时间',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`notice_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统通知表';

-- 插入初始数据

-- 插入快递站点数据
INSERT INTO `express_station` (`station_name`, `station_type`, `campus`, `location`, `open_time`, `contact_phone`, `status`, `create_time`, `update_time`) VALUES
('菜鸟驿站(东区)', '菜鸟驿站', '东校区', '东区生活区1号楼下', '08:00-22:00', '13800138001', 1, NOW(), NOW()),
('京东快递站(西区)', '京东站点', '西校区', '西区食堂旁', '09:00-21:00', '13800138002', 1, NOW(), NOW()),
('顺丰快递点(南区)', '顺丰站点', '南校区', '南区宿舍楼群中心', '08:30-21:30', '13800138003', 1, NOW(), NOW()),
('圆通快递站(北区)', '圆通站点', '北校区', '北区教学楼附近', '08:00-20:00', '***********', 1, NOW(), NOW());

-- 插入管理员用户
INSERT INTO `user` (`username`, `password`, `phone`, `student_id`, `real_name`, `user_type`, `status`, `audit_status`, `create_time`, `update_time`) VALUES
('admin', 'e10adc3949ba59abbe56e057f20f883e', '***********', 'ADMIN001', '系统管理员', 3, 1, 1, NOW(), NOW());

-- 插入测试用户数据
INSERT INTO `user` (`username`, `password`, `phone`, `student_id`, `real_name`, `user_type`, `status`, `audit_status`, `alipay_account`, `wechat_account`, `create_time`, `update_time`) VALUES
('student001', 'e10adc3949ba59abbe56e057f20f883e', '***********', '**********', '张三', 1, 1, 1, NULL, NULL, NOW(), NOW()),
('pickup001', 'e10adc3949ba59abbe56e057f20f883e', '***********', '**********', '李四', 2, 1, 1, '<EMAIL>', 'pickup001', NOW(), NOW()),
('pickup002', 'e10adc3949ba59abbe56e057f20f883e', '***********', '**********', '王五', 2, 1, 1, '<EMAIL>', 'pickup002', NOW(), NOW());

-- 代取员接单设置表
CREATE TABLE `pickup_settings` (
  `setting_id` bigint NOT NULL AUTO_INCREMENT COMMENT '设置ID',
  `user_id` bigint NOT NULL COMMENT '代取员用户ID',
  `accept_orders` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否接单：1-是，0-否',
  `work_time_start` varchar(10) DEFAULT '08:00' COMMENT '工作开始时间',
  `work_time_end` varchar(10) DEFAULT '22:00' COMMENT '工作结束时间',
  `campus_range` varchar(500) DEFAULT NULL COMMENT '接单校区范围（JSON格式）',
  `min_fee` decimal(10,2) DEFAULT '3.00' COMMENT '最低接单费用',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注说明',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`setting_id`),
  UNIQUE KEY `uk_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='代取员接单设置表';

-- 用户通知读取记录表
CREATE TABLE `user_notice_read` (
  `read_id` bigint NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `notice_id` bigint NOT NULL COMMENT '通知ID',
  `read_time` datetime NOT NULL COMMENT '阅读时间',
  PRIMARY KEY (`read_id`),
  UNIQUE KEY `uk_user_notice` (`user_id`, `notice_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户通知读取记录表';

-- 插入测试地址数据
INSERT INTO `address` (`user_id`, `campus`, `building`, `room`, `detail_address`, `contact_name`, `contact_phone`, `is_default`, `create_time`, `update_time`) VALUES
(2, '东校区', '1号宿舍楼', '101', '靠近楼梯口', '张三', '***********', 1, NOW(), NOW()),
(3, '西校区', '2号宿舍楼', '201', '走廊中间', '李四', '***********', 1, NOW(), NOW()),
(4, '南校区', '3号宿舍楼', '301', '阳台朝南', '王五', '***********', 1, NOW(), NOW());

-- 插入代取员接单设置数据
INSERT INTO `pickup_settings` (`user_id`, `accept_orders`, `work_time_start`, `work_time_end`, `campus_range`, `min_fee`, `create_time`, `update_time`) VALUES
(3, 1, '08:00', '22:00', '["main", "south"]', 3.00, NOW(), NOW()),
(4, 1, '09:00', '21:00', '["main", "north"]', 3.50, NOW(), NOW());

-- 插入系统通知数据
INSERT INTO `system_notice` (`title`, `content`, `notice_type`, `target_type`, `status`, `publish_time`, `create_time`, `update_time`) VALUES
('欢迎使用校园快递代取系统', '感谢您注册使用我们的服务，如有问题请联系客服。', 1, 1, 1, NOW(), NOW(), NOW()),
('代取员申请审核通过', '恭喜您成为我们的代取员，请及时完善个人信息。', 2, 2, 1, NOW(), NOW(), NOW()),
('系统维护通知', '系统将于今晚22:00-24:00进行维护升级。', 1, 1, 1, NOW(), NOW(), NOW());
