package com.example.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.example.enums.Role;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;

import java.time.LocalDateTime;
import java.math.BigDecimal;

/**
 * 用户实体类 - 对应数据库user表
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("user")
public class SysUser extends BaseEntity{
    @TableId(type = IdType.AUTO)
    private Long userId;
    private String username;
    private String password;
    private String realName;
    private String phone;
    private String email;
    private String avatar;
    @EnumValue
    private Role userType;


    @TableField(exist = false)
    private String campus; // 校区
    @TableField(exist = false,value = "dorm_building")
    private String dormBuilding; // 宿舍楼

    @TableField(exist = false,value = "room_number")
    private String roomNumber; // 房间号
    
    @TableField(exist = false,value = "id_card")
    private String idCard;
    
    @TableField(exist = false,value = "alipay_account")
    private String alipayAccount;
    
    @TableField(exist = false,value = "wechat_account")
    private String wechatAccount;
    
    @TableField(exist = false,value = "audit_status")
    private Integer auditStatus; // 审核状态：0-待审核，1-审核通过，2-审核拒绝

    @TableField(exist = false,value = "audit_comment")
    private String auditComment; // 审核备注

    @TableField(exist = false,value = "audit_reason")
    private String auditReason; // 审核原因

    @TableField(exist = false,value = "audit_time")
    private LocalDateTime auditTime; // 审核时间

    @TableField(exist = false,value = "courier_level")
    private Integer courierLevel; // 代取员等级：1-初级，2-中级，3-高级，4-专家

    @TableField(exist = false,value = "total_earnings")
    private BigDecimal totalEarnings; // 累计收入

    @TableField(exist = false,value = "monthly_earnings")
    private BigDecimal monthlyEarnings; // 本月收入

    @TableField(exist = false,value = "completed_orders")
    private Integer completedOrders; // 完成订单数

    @TableField(exist = false,value = "cancelled_orders")
    private Integer cancelledOrders; // 取消订单数

    @TableField(exist = false,value = "complaints")
    private Integer complaints; // 投诉次数

    @TableField(exist = false,value = "student_card_image")
    private String studentCardImage; // 学生证照片

    @TableField(exist = false,value = "id_card_image")
    private String idCardImage; // 身份证照片
    @TableField(exist = false)
    private BigDecimal balance; // 余额
    @TableField(exist = false)
    private BigDecimal rating; // 评分

    @TableField(exist = false,value = "order_count")
    private Integer orderCount; // 完成订单数

    @TableField(exist = false,value = "create_time")
    private LocalDateTime createTime;

    @TableField(exist = false,value = "update_time")
    private LocalDateTime updateTime;

}