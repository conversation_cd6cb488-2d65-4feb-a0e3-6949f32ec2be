package com.example.dto;

import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.DecimalMin;
import java.math.BigDecimal;
import java.util.List;

/**
 * 代取员接单设置DTO
 */
@Data
public class PickupSettingsDTO {
    
    @NotNull(message = "接单状态不能为空")
    private Boolean acceptOrders; // 是否接单
    
    private String workTimeStart; // 工作开始时间
    private String workTimeEnd;   // 工作结束时间
    
    private List<String> campusRange; // 接单校区范围
    
    @DecimalMin(value = "0.01", message = "最低接单费用必须大于0")
    private BigDecimal minFee; // 最低接单费用
    
    private String remark; // 备注说明
}