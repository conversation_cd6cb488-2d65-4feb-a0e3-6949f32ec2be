package com.example.controller;

import com.example.config.ExpressConfig;
import com.example.dto.FileDeleteDTO;
import com.example.util.FileUtils;
import com.example.util.R;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.nio.file.Paths;

@RestController
@RequestMapping("/file")
public class FileController {

    @PostMapping("/upload")
    public R<?> upload(@RequestParam(value = "file",required = false) MultipartFile file,
                       @RequestParam(value = "path") String path){
        try
        {
            // 上传文件路径
            String filePath = ExpressConfig.getProfile()+path;
            File desc = new File(filePath);
            if (!desc.exists())
            {
                if (!desc.getParentFile().exists())
                {
                    desc.getParentFile().mkdirs();
                }
            }
            String absPath = desc.getAbsolutePath();
            file.transferTo(Paths.get(absPath));
            return R.ok(filePath);

        }
        catch (Exception e)
        {
            return R.fail(e.getMessage());
        }
    }

    @GetMapping("/download")
    public void fileDownload(String path, HttpServletResponse response)
    {
        Boolean delete = false;
        try
        {
            String realFileName = FileUtils.getName(path);
            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            FileUtils.setAttachmentResponseHeader(response, realFileName);
            FileUtils.writeBytes(path, response.getOutputStream());
            if (delete)
            {
                FileUtils.deleteFile(path);
            }
        }
        catch (Exception e)
        {
            throw new RuntimeException("下载文件失败:" + e);
        }
    }

    @PostMapping("delete")
    public R<?> deleteFile(@RequestBody FileDeleteDTO fileDeleteDTO) {
        String filePath = ExpressConfig.getProfile() + fileDeleteDTO.getPath();
        FileUtils.deleteFile(filePath);
        return R.ok();
    }
}
