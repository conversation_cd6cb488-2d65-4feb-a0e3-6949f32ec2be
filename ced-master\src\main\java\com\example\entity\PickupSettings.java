package com.example.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 代取员接单设置实体类
 */
@Data
@TableName("pickup_settings")
public class PickupSettings {
    
    @TableId(type = IdType.AUTO)
    @TableField("setting_id")
    private Long settingId;
    
    @TableField("user_id")
    private Long userId; // 代取员用户ID
    
    @TableField("accept_orders")
    private Boolean acceptOrders; // 是否接单
    
    @TableField("work_time_start")
    private String workTimeStart; // 工作开始时间
    
    @TableField("work_time_end")
    private String workTimeEnd; // 工作结束时间
    
    @TableField("campus_range")
    private String campusRange; // 接单校区范围（JSON格式）
    
    @TableField("min_fee")
    private BigDecimal minFee; // 最低接单费用
    
    private String remark; // 备注说明
    
    @TableField("create_time")
    private LocalDateTime createTime;
    
    @TableField("update_time")
    private LocalDateTime updateTime;
}