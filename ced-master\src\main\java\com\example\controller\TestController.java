package com.example.controller;

import com.example.entity.ExpressStation;
import com.example.mapper.ExpressStationMapper;
import com.example.mapper.UserMapper;
import com.example.util.R;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 测试控制器
 */
@RestController
@RequestMapping("/api/test")
public class TestController {
    
    @Autowired
    private UserMapper userMapper;
    
    @Autowired
    private ExpressStationMapper expressStationMapper;
    
    /**
     * 系统健康检查
     */
    @GetMapping("/health")
    public R<String> health() {
        return R.ok("系统运行正常");
    }
    
    /**
     * 获取系统信息
     */
    @GetMapping("/info")
    public R<Map<String, Object>> getSystemInfo() {
        Map<String, Object> info = new HashMap<>();
        info.put("systemName", "校园快递代取系统");
        info.put("version", "1.0.0");
        info.put("author", "System Developer");
        info.put("description", "为校园学生提供便捷的快递代取服务");
        
        return R.ok(info);
    }
}