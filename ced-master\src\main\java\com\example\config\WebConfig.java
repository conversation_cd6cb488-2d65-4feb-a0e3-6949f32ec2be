package com.example.config;

import com.example.interceptor.JwtInterceptor;
import com.example.interceptor.RoleInterceptor;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.annotation.Resource;

@Configuration
public class WebConfig implements WebMvcConfigurer {
    @Resource
    private RoleInterceptor roleInterceptor;
    @Resource
    private JwtInterceptor jwtInterceptor;

    /**
     * 注册拦截器
     * @param registry
     * 当配置文件配置了 context-path 时，
     * 拦截器配置中的路径不需要包含这个前缀，
     * 因为拦截器工作在 context-path 解析之后
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 注册拦截器
        registry.addInterceptor(jwtInterceptor)
                .addPathPatterns("/**")
                .excludePathPatterns("/login", "/register","/captcha","/roleList", "/files/**");
        // 权限拦截器
        registry.addInterceptor(roleInterceptor)
                .addPathPatterns("/**");
    }

    /**
     * 配置静态资源映射
     * @param registry
     */
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 配置文件访问路径映射
        String uploadPath = ExpressConfig.getProfile();
        registry.addResourceHandler("/files/**")
                .addResourceLocations("file:" + uploadPath + "/");
    }
}