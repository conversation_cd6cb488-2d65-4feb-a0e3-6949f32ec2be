-- 为用户表添加校区和宿舍字段
-- 这些字段用于快速访问用户的基本位置信息，避免每次都要关联address表

ALTER TABLE `user` 
ADD COLUMN `campus` varchar(50) DEFAULT NULL COMMENT '校区' AFTER `real_name`,
ADD COLUMN `dorm_building` varchar(50) DEFAULT NULL COMMENT '宿舍楼' AFTER `campus`,
ADD COLUMN `room_number` varchar(20) DEFAULT NULL COMMENT '房间号' AFTER `dorm_building`;

-- 从address表中迁移默认地址的校区和宿舍信息到user表
UPDATE `user` u 
INNER JOIN `address` a ON u.user_id = a.user_id 
SET u.campus = a.campus, 
    u.dorm_building = a.building, 
    u.room_number = a.room 
WHERE a.is_default = 1;
