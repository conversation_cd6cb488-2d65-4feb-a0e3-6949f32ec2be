package com.example.dto;

import lombok.Data;
import java.math.BigDecimal;

/**
 * 用户统计信息DTO
 */
@Data
public class UserStatisticsDTO {
    
    private Integer totalOrders;        // 总订单数
    private Integer completedOrders;    // 已完成订单数
    private Integer cancelledOrders;    // 已取消订单数
    private Integer pendingOrders;      // 待处理订单数
    
    private BigDecimal totalSpent;      // 总消费（学生用户）
    private BigDecimal totalEarnings;   // 总收益（代取员）
    private BigDecimal currentBalance;  // 当前余额
    
    private Integer pickupOrders;       // 代取订单数（代取员）
    private BigDecimal averageRating;   // 平均评分（代取员）
    
    private String joinDate;            // 注册时间
    private Integer activeDays;         // 活跃天数
}