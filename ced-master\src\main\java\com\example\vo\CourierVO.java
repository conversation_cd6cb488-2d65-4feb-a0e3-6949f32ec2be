package com.example.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 代取员视图对象
 */
@Data
public class CourierVO {
    
    private Long userId;
    
    private String username;
    
    private String realName;
    
    private String studentId;
    
    private String phone;
    
    private String idCard;
    
    private String alipayAccount;
    
    private Integer orderCount;
    
    private Integer completedOrders;
    
    private Integer cancelledOrders;
    
    private Float rating;
    
    private Float totalEarnings;
    
    private Float monthlyEarnings;
    
    private Integer complaints;
    
    private Integer status;
    
    private String statusText;
    
    private Integer auditStatus;
    
    private String auditStatusText;
    
    private String auditComment;
    
    private LocalDateTime createTime;
    
    private LocalDateTime updateTime;
}