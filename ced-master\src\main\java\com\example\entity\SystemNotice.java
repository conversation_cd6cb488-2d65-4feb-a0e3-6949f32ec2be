package com.example.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 系统通知实体类 - 对应数据库system_notice表
 */
@Data
@TableName("system_notice")
public class SystemNotice {
    @TableId(type = IdType.AUTO)
    @TableField("notice_id")
    private Long noticeId;
    
    private String title; // 通知标题
    private String content; // 通知内容
    
    @TableField("notice_type")
    private Integer noticeType; // 通知类型：1-系统公告，2-订单通知，3-账户通知
    
    @TableField("target_type")
    private Integer targetType; // 接收对象：1-全部用户，2-普通用户，3-代取员
    
    @TableField("target_user_id")
    private Long targetUserId; // 特定用户ID，为空则群发
    
    private Integer status; // 状态：1-已发布，0-草稿
    
    @TableField("publish_time")
    private LocalDateTime publishTime; // 发布时间
    
    @TableField("create_time")
    private LocalDateTime createTime;
    
    @TableField("update_time")
    private LocalDateTime updateTime;
}