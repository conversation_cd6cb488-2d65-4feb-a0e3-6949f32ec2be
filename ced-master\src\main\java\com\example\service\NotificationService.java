package com.example.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.entity.SystemNotice;
import com.example.entity.UserNotificationRead;
import com.example.mapper.SystemNoticeMapper;
import com.example.mapper.UserNotificationReadMapper;
import com.example.vo.NotificationVO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 通知服务
 */
@Service
public class NotificationService {

    @Resource
    private SystemNoticeMapper systemNoticeMapper;

    @Resource
    private UserNotificationReadMapper userNotificationReadMapper;

    /**
     * 获取用户通知列表
     */
    public Map<String, Object> getUserNotifications(Long userId, Integer page, Integer size, Integer noticeType) {
        Page<SystemNotice> pageInfo = new Page<>(page, size);
        LambdaQueryWrapper<SystemNotice> queryWrapper = new LambdaQueryWrapper<>();

        // 查询全部用户通知或特定用户通知
        queryWrapper.and(wrapper ->
            wrapper.eq(SystemNotice::getTargetType, 1) // 全部用户
                   .or()
                   .eq(SystemNotice::getTargetUserId, userId) // 特定用户
        );

        if (noticeType != null) {
            queryWrapper.eq(SystemNotice::getNoticeType, noticeType);
        }

        queryWrapper.eq(SystemNotice::getStatus, 1) // 已发布
                   .orderByDesc(SystemNotice::getCreateTime);

        IPage<SystemNotice> result = systemNoticeMapper.selectPage(pageInfo, queryWrapper);

        // 获取用户已读的通知ID集合
        Set<Long> readNoticeIds = getUserReadNoticeIds(userId);

        // 转换为NotificationVO并设置已读状态
        List<NotificationVO> notificationVOs = result.getRecords().stream()
                .map(notice -> {
                    NotificationVO vo = new NotificationVO();
                    BeanUtils.copyProperties(notice, vo);
                    vo.setIsRead(readNoticeIds.contains(notice.getNoticeId()));

                    // 如果已读，获取读取时间
                    if (vo.getIsRead()) {
                        LambdaQueryWrapper<UserNotificationRead> readWrapper = new LambdaQueryWrapper<>();
                        readWrapper.eq(UserNotificationRead::getUserId, userId)
                                  .eq(UserNotificationRead::getNoticeId, notice.getNoticeId());
                        UserNotificationRead readRecord = userNotificationReadMapper.selectOne(readWrapper);
                        if (readRecord != null) {
                            vo.setReadTime(readRecord.getReadTime());
                        }
                    }

                    return vo;
                })
                .collect(Collectors.toList());

        Map<String, Object> response = new HashMap<>();
        response.put("records", notificationVOs);
        response.put("total", result.getTotal());
        response.put("pages", result.getPages());
        response.put("current", result.getCurrent());
        response.put("size", result.getSize());

        return response;
    }

    /**
     * 获取用户已读的通知ID集合
     */
    private Set<Long> getUserReadNoticeIds(Long userId) {
        LambdaQueryWrapper<UserNotificationRead> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserNotificationRead::getUserId, userId);

        List<UserNotificationRead> readRecords = userNotificationReadMapper.selectList(queryWrapper);
        return readRecords.stream()
                .map(UserNotificationRead::getNoticeId)
                .collect(Collectors.toSet());
    }

    /**
     * 获取未读通知数量
     */
    public Map<String, Object> getUnreadCount(Long userId) {
        // 获取用户可见的所有通知
        LambdaQueryWrapper<SystemNotice> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.and(wrapper ->
            wrapper.eq(SystemNotice::getTargetType, 1)
                   .or()
                   .eq(SystemNotice::getTargetUserId, userId)
        );
        queryWrapper.eq(SystemNotice::getStatus, 1);

        List<SystemNotice> allNotices = systemNoticeMapper.selectList(queryWrapper);

        // 获取用户已读的通知ID集合
        Set<Long> readNoticeIds = getUserReadNoticeIds(userId);

        // 计算未读数量
        long unreadCount = allNotices.stream()
                .filter(notice -> !readNoticeIds.contains(notice.getNoticeId()))
                .count();

        Map<String, Object> result = new HashMap<>();
        result.put("unreadCount", unreadCount);
        return result;
    }

    /**
     * 标记通知为已读
     */
    @Transactional
    public void markAsRead(Long userId, Long noticeId) {
        // 检查是否已经标记为已读
        LambdaQueryWrapper<UserNotificationRead> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserNotificationRead::getUserId, userId)
                   .eq(UserNotificationRead::getNoticeId, noticeId);

        UserNotificationRead existingRecord = userNotificationReadMapper.selectOne(queryWrapper);
        if (existingRecord == null) {
            // 创建已读记录
            UserNotificationRead readRecord = new UserNotificationRead();
            readRecord.setUserId(userId);
            readRecord.setNoticeId(noticeId);
            readRecord.setReadTime(LocalDateTime.now());
            readRecord.setCreateTime(LocalDateTime.now());

            userNotificationReadMapper.insert(readRecord);
        }
    }

    /**
     * 标记所有通知为已读
     */
    @Transactional
    public void markAllAsRead(Long userId) {
        // 获取用户可见的所有通知
        LambdaQueryWrapper<SystemNotice> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.and(wrapper ->
            wrapper.eq(SystemNotice::getTargetType, 1)
                   .or()
                   .eq(SystemNotice::getTargetUserId, userId)
        );
        queryWrapper.eq(SystemNotice::getStatus, 1);

        List<SystemNotice> allNotices = systemNoticeMapper.selectList(queryWrapper);

        // 获取用户已读的通知ID集合
        Set<Long> readNoticeIds = getUserReadNoticeIds(userId);

        // 标记未读的通知为已读
        LocalDateTime now = LocalDateTime.now();
        for (SystemNotice notice : allNotices) {
            if (!readNoticeIds.contains(notice.getNoticeId())) {
                UserNotificationRead readRecord = new UserNotificationRead();
                readRecord.setUserId(userId);
                readRecord.setNoticeId(notice.getNoticeId());
                readRecord.setReadTime(now);
                readRecord.setCreateTime(now);

                userNotificationReadMapper.insert(readRecord);
            }
        }
    }

    /**
     * 删除通知（软删除，实际是标记为不可见）
     */
    @Transactional
    public void deleteNotification(Long userId, Long noticeId) {
        // 这里可以创建一个用户通知删除记录表来记录用户删除的通知
        // 暂时通过标记已读来实现"删除"效果
        markAsRead(userId, noticeId);
    }

    /**
     * 批量标记通知为已读
     */
    @Transactional
    public void markMultipleAsRead(Long userId, List<Long> noticeIds) {
        LocalDateTime now = LocalDateTime.now();

        for (Long noticeId : noticeIds) {
            // 检查是否已经标记为已读
            LambdaQueryWrapper<UserNotificationRead> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(UserNotificationRead::getUserId, userId)
                       .eq(UserNotificationRead::getNoticeId, noticeId);

            UserNotificationRead existingRecord = userNotificationReadMapper.selectOne(queryWrapper);
            if (existingRecord == null) {
                // 创建已读记录
                UserNotificationRead readRecord = new UserNotificationRead();
                readRecord.setUserId(userId);
                readRecord.setNoticeId(noticeId);
                readRecord.setReadTime(now);
                readRecord.setCreateTime(now);

                userNotificationReadMapper.insert(readRecord);
            }
        }
    }

    /**
     * 批量删除通知
     */
    @Transactional
    public void deleteMultipleNotifications(Long userId, List<Long> noticeIds) {
        // 批量标记为已读来实现"删除"效果
        markMultipleAsRead(userId, noticeIds);
    }

    /**
     * 创建系统通知
     */
    @Transactional
    public void createSystemNotification(String title, String content, Integer noticeType,
                                       Integer targetType, Long targetUserId) {
        SystemNotice notice = new SystemNotice();
        notice.setTitle(title);
        notice.setContent(content);
        notice.setNoticeType(noticeType);
        notice.setTargetType(targetType);
        notice.setTargetUserId(targetUserId);
        notice.setStatus(1); // 直接发布
        notice.setPublishTime(LocalDateTime.now());
        notice.setCreateTime(LocalDateTime.now());
        notice.setUpdateTime(LocalDateTime.now());

        systemNoticeMapper.insert(notice);
    }

    /**
     * 获取通知详情
     */
    public NotificationVO getNotificationDetail(Long userId, Long noticeId) {
        SystemNotice notice = systemNoticeMapper.selectById(noticeId);
        if (notice == null || notice.getStatus() != 1) {
            return null;
        }

        // 检查用户是否有权限查看此通知
        if (notice.getTargetType() != 1 && !notice.getTargetUserId().equals(userId)) {
            return null;
        }

        NotificationVO vo = new NotificationVO();
        BeanUtils.copyProperties(notice, vo);

        // 检查是否已读
        LambdaQueryWrapper<UserNotificationRead> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserNotificationRead::getUserId, userId)
                   .eq(UserNotificationRead::getNoticeId, noticeId);
        UserNotificationRead readRecord = userNotificationReadMapper.selectOne(queryWrapper);

        vo.setIsRead(readRecord != null);
        if (readRecord != null) {
            vo.setReadTime(readRecord.getReadTime());
        }

        return vo;
    }
}