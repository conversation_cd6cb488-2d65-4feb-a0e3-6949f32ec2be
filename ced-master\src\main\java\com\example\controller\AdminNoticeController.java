package com.example.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.entity.SystemNotice;
import com.example.mapper.SystemNoticeMapper;

import com.example.util.R;
import com.example.vo.NoticeVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 管理员公告管理控制器
 */
@RestController
@RequestMapping("/admin/notices")
public class AdminNoticeController {
    
    @Autowired
    private SystemNoticeMapper systemNoticeMapper;


    
    /**
     * 获取公告列表（支持分页和搜索）
     */
    @GetMapping
    public R<Map<String, Object>> getNotices(
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) Integer status,
            @RequestParam(required = false) Integer noticeType,
            @RequestParam(required = false) Integer targetType) {

        Page<SystemNotice> page = new Page<>(current, size);
        LambdaQueryWrapper<SystemNotice> query = new LambdaQueryWrapper<>();

        // 搜索条件
        if (StringUtils.hasText(keyword)) {
            query.and(wrapper -> wrapper.like(SystemNotice::getTitle, keyword)
                    .or().like(SystemNotice::getContent, keyword));
        }
        if (status != null) {
            query.eq(SystemNotice::getStatus, status);
        }
        if (noticeType != null) {
            query.eq(SystemNotice::getNoticeType, noticeType);
        }
        if (targetType != null) {
            query.eq(SystemNotice::getTargetType, targetType);
        }

        query.orderByDesc(SystemNotice::getCreateTime);

        IPage<SystemNotice> pageResult = systemNoticeMapper.selectPage(page, query);

        List<NoticeVO> noticeVOs = pageResult.getRecords().stream().map(notice -> {
            NoticeVO noticeVO = new NoticeVO();
            // 映射字段
            noticeVO.setId(notice.getNoticeId());
            noticeVO.setTitle(notice.getTitle());
            noticeVO.setContent(notice.getContent());
            noticeVO.setType(getNoticeTypeText(notice.getNoticeType()));
            noticeVO.setTarget(getTargetTypeText(notice.getTargetType()));
            noticeVO.setStatus(notice.getStatus() == 1 ? "published" : "draft");
            noticeVO.setReadCount(0); // 移除阅读统计功能
            noticeVO.setCreateTime(notice.getCreateTime());
            noticeVO.setUpdateTime(notice.getUpdateTime());
            return noticeVO;
        }).collect(Collectors.toList());

        Map<String, Object> result = new HashMap<>();
        result.put("records", noticeVOs);
        result.put("total", pageResult.getTotal());
        result.put("current", pageResult.getCurrent());
        result.put("size", pageResult.getSize());
        result.put("pages", pageResult.getPages());

        return R.ok(result);
    }
    
    /**
     * 创建公告
     */
    @PostMapping
    public R<String> createNotice(@RequestBody Map<String, Object> params) {
        String title = (String) params.get("title");
        String content = (String) params.get("content");
        String type = (String) params.get("type");
        Object targetObj = params.get("target");
        String status = (String) params.get("status");

        if (title == null || title.isEmpty() || content == null || content.isEmpty() ||
            type == null || type.isEmpty() || targetObj == null) {
            return R.fail("参数不完整");
        }

        SystemNotice notice = new SystemNotice();
        notice.setTitle(title);
        notice.setContent(content);
        notice.setNoticeType(parseNoticeType(type));
        notice.setTargetType(parseTargetType(targetObj));
        notice.setStatus("published".equals(status) ? 1 : 0);
        notice.setPublishTime("published".equals(status) ? LocalDateTime.now() : null);
        notice.setCreateTime(LocalDateTime.now());
        notice.setUpdateTime(LocalDateTime.now());

        systemNoticeMapper.insert(notice);
        return R.ok("公告创建成功");
    }
    
    /**
     * 更新公告
     */
    @PutMapping("/{noticeId}")
    public R<String> updateNotice(@PathVariable Long noticeId, @RequestBody Map<String, Object> params) {
        SystemNotice notice = systemNoticeMapper.selectById(noticeId);
        if (notice == null) {
            return R.fail("公告不存在");
        }

        String title = (String) params.get("title");
        String content = (String) params.get("content");
        String type = (String) params.get("type");
        Object targetObj = params.get("target");
        String status = (String) params.get("status");

        if (title != null && !title.isEmpty()) {
            notice.setTitle(title);
        }
        if (content != null && !content.isEmpty()) {
            notice.setContent(content);
        }
        if (type != null && !type.isEmpty()) {
            notice.setNoticeType(parseNoticeType(type));
        }
        if (targetObj != null) {
            notice.setTargetType(parseTargetType(targetObj));
        }
        if (status != null && !status.isEmpty()) {
            notice.setStatus("published".equals(status) ? 1 : 0);
            if ("published".equals(status) && notice.getPublishTime() == null) {
                notice.setPublishTime(LocalDateTime.now());
            }
        }

        notice.setUpdateTime(LocalDateTime.now());
        systemNoticeMapper.updateById(notice);

        return R.ok("公告更新成功");
    }
    
    /**
     * 删除公告
     */
    @DeleteMapping("/{noticeId}")
    public R<String> deleteNotice(@PathVariable Long noticeId) {
        SystemNotice notice = systemNoticeMapper.selectById(noticeId);
        if (notice == null) {
            return R.fail("公告不存在");
        }

        systemNoticeMapper.deleteById(noticeId);
        return R.ok("公告删除成功");
    }

    /**
     * 发布公告
     */
    @PostMapping("/{noticeId}/publish")
    public R<String> publishNotice(@PathVariable Long noticeId) {
        SystemNotice notice = systemNoticeMapper.selectById(noticeId);
        if (notice == null) {
            return R.fail("公告不存在");
        }

        notice.setStatus(1);
        notice.setPublishTime(LocalDateTime.now());
        notice.setUpdateTime(LocalDateTime.now());
        systemNoticeMapper.updateById(notice);

        return R.ok("公告发布成功");
    }

    /**
     * 撤回公告
     */
    @PostMapping("/{noticeId}/unpublish")
    public R<String> unpublishNotice(@PathVariable Long noticeId) {
        SystemNotice notice = systemNoticeMapper.selectById(noticeId);
        if (notice == null) {
            return R.fail("公告不存在");
        }

        notice.setStatus(0);
        notice.setUpdateTime(LocalDateTime.now());
        systemNoticeMapper.updateById(notice);

        return R.ok("公告撤回成功");
    }

    /**
     * 批量删除公告
     */
    @PostMapping("/batch-delete")
    public R<String> batchDeleteNotices(@RequestBody Map<String, Object> params) {
        @SuppressWarnings("unchecked")
        List<Long> noticeIds = (List<Long>) params.get("noticeIds");

        if (noticeIds == null || noticeIds.isEmpty()) {
            return R.fail("请选择要删除的公告");
        }

        systemNoticeMapper.deleteBatchIds(noticeIds);
        return R.ok("批量删除成功");
    }

    /**
     * 批量发布公告
     */
    @PostMapping("/batch-publish")
    public R<String> batchPublishNotices(@RequestBody Map<String, Object> params) {
        @SuppressWarnings("unchecked")
        List<Long> noticeIds = (List<Long>) params.get("noticeIds");

        if (noticeIds == null || noticeIds.isEmpty()) {
            return R.fail("请选择要发布的公告");
        }

        for (Long noticeId : noticeIds) {
            SystemNotice notice = systemNoticeMapper.selectById(noticeId);
            if (notice != null) {
                notice.setStatus(1);
                notice.setPublishTime(LocalDateTime.now());
                notice.setUpdateTime(LocalDateTime.now());
                systemNoticeMapper.updateById(notice);
            }
        }

        return R.ok("批量发布成功");
    }

    /**
     * 批量撤回公告
     */
    @PostMapping("/batch-unpublish")
    public R<String> batchUnpublishNotices(@RequestBody Map<String, Object> params) {
        @SuppressWarnings("unchecked")
        List<Long> noticeIds = (List<Long>) params.get("noticeIds");

        if (noticeIds == null || noticeIds.isEmpty()) {
            return R.fail("请选择要撤回的公告");
        }

        for (Long noticeId : noticeIds) {
            SystemNotice notice = systemNoticeMapper.selectById(noticeId);
            if (notice != null) {
                notice.setStatus(0);
                notice.setUpdateTime(LocalDateTime.now());
                systemNoticeMapper.updateById(notice);
            }
        }

        return R.ok("批量撤回成功");
    }

    /**
     * 获取公告统计信息
     */
    @GetMapping("/statistics")
    public R<Map<String, Object>> getNoticeStatistics() {
        Map<String, Object> stats = new HashMap<>();

        // 总公告数
        int totalNotices = systemNoticeMapper.selectCount(null);

        // 已发布公告数
        LambdaQueryWrapper<SystemNotice> publishedQuery = new LambdaQueryWrapper<>();
        publishedQuery.eq(SystemNotice::getStatus, 1);
        int publishedNotices = systemNoticeMapper.selectCount(publishedQuery);

        // 草稿公告数
        LambdaQueryWrapper<SystemNotice> draftQuery = new LambdaQueryWrapper<>();
        draftQuery.eq(SystemNotice::getStatus, 0);
        int draftNotices = systemNoticeMapper.selectCount(draftQuery);

        // 今日发布公告数
        LambdaQueryWrapper<SystemNotice> todayQuery = new LambdaQueryWrapper<>();
        todayQuery.eq(SystemNotice::getStatus, 1)
                .ge(SystemNotice::getPublishTime, LocalDateTime.now().toLocalDate().atStartOfDay());
        int todayPublished = systemNoticeMapper.selectCount(todayQuery);

        stats.put("totalNotices", totalNotices);
        stats.put("publishedNotices", publishedNotices);
        stats.put("draftNotices", draftNotices);
        stats.put("todayPublished", todayPublished);

        return R.ok(stats);
    }

    /**
     * 获取通知类型文本
     */
    private String getNoticeTypeText(Integer noticeType) {
        if (noticeType == null) return "system";
        switch (noticeType) {
            case 1: return "system";
            case 2: return "feature";
            case 3: return "important";
            default: return "system";
        }
    }

    /**
     * 获取目标类型文本
     */
    private String getTargetTypeText(Integer targetType) {
        if (targetType == null) return "all";
        switch (targetType) {
            case 1: return "all";
            case 2: return "student";
            case 3: return "courier";
            default: return "all";
        }
    }

    /**
     * 解析通知类型
     */
    private Integer parseNoticeType(String type) {
        if (type == null) return 1;
        switch (type) {
            case "system": return 1;
            case "feature": return 2;
            case "important": return 3;
            default: return 1;
        }
    }

    /**
     * 解析目标类型
     */
    private Integer parseTargetType(Object targetObj) {
        if (targetObj == null) return 1;

        // 处理数组格式（前端checkbox-group发送的格式）
        if (targetObj instanceof java.util.List) {
            @SuppressWarnings("unchecked")
            java.util.List<String> targetList = (java.util.List<String>) targetObj;
            if (targetList.isEmpty()) return 1;

            // 如果包含student和courier，则为全部用户
            if (targetList.contains("student") && targetList.contains("courier")) {
                return 1; // 全部用户
            } else if (targetList.contains("student")) {
                return 2; // 学生
            } else if (targetList.contains("courier")) {
                return 3; // 代取员
            }
            return 1;
        }

        // 处理字符串格式（兼容旧格式）
        String target = targetObj.toString();
        switch (target) {
            case "all": return 1;
            case "student": return 2;
            case "courier": return 3;
            default: return 1;
        }
    }
}