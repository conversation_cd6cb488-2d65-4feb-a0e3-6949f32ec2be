package com.example.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.dto.RechargeDTO;
import com.example.entity.SysUser;
import com.example.entity.Wallet;
import com.example.mapper.UserMapper;
import com.example.mapper.WalletMapper;
import com.example.util.IdUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 钱包服务
 */
@Service
public class WalletService {

    @Resource
    private WalletMapper walletMapper;
    
    @Resource
    private UserMapper userMapper;

    /**
     * 获取用户余额
     */
    public Map<String, Object> getUserBalance(Long userId) {
        SysUser user = userMapper.selectById(userId);
        Map<String, Object> result = new HashMap<>();
        result.put("balance", user != null ? user.getBalance() : BigDecimal.ZERO);
        result.put("userId", userId);
        return result;
    }

    /**
     * 获取用户交易记录
     */
    public Map<String, Object> getUserTransactions(Long userId, Integer page, Integer size) {
        Page<Wallet> pageInfo = new Page<>(page, size);
        LambdaQueryWrapper<Wallet> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Wallet::getUserId, userId)
                   .orderByDesc(Wallet::getCreateTime);
        
        IPage<Wallet> result = walletMapper.selectPage(pageInfo, queryWrapper);
        
        Map<String, Object> response = new HashMap<>();
        response.put("records", result.getRecords());
        response.put("total", result.getTotal());
        response.put("pages", result.getPages());
        response.put("current", result.getCurrent());
        response.put("size", result.getSize());
        
        return response;
    }

    /**
     * 充值
     */
    @Transactional
    public void recharge(Long userId, RechargeDTO rechargeDTO) {
        SysUser user = userMapper.selectById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        BigDecimal oldBalance = user.getBalance() != null ? user.getBalance() : BigDecimal.ZERO;
        BigDecimal newBalance = oldBalance.add(rechargeDTO.getAmount());

        // 更新用户余额
        user.setBalance(newBalance);
        user.setUpdateTime(LocalDateTime.now());
        userMapper.updateById(user);

        // 记录交易流水
        Wallet walletRecord = new Wallet();
        walletRecord.setUserId(userId);
        walletRecord.setTransactionType(1); // 充值
        walletRecord.setAmount(rechargeDTO.getAmount());
        walletRecord.setBalanceBefore(oldBalance);
        walletRecord.setBalanceAfter(newBalance);
        walletRecord.setDescription("账户充值 - " + rechargeDTO.getPayMethod());
        walletRecord.setTransactionNo(generateTransactionNo());
        walletRecord.setStatus(1); // 成功
        walletRecord.setCreateTime(LocalDateTime.now());
        
        walletMapper.insert(walletRecord);
    }

    /**
     * 提现
     */
    @Transactional
    public void withdraw(Long userId, BigDecimal amount) {
        SysUser user = userMapper.selectById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        BigDecimal currentBalance = user.getBalance() != null ? user.getBalance() : BigDecimal.ZERO;
        if (currentBalance.compareTo(amount) < 0) {
            throw new RuntimeException("余额不足");
        }

        BigDecimal newBalance = currentBalance.subtract(amount);

        // 更新用户余额
        user.setBalance(newBalance);
        user.setUpdateTime(LocalDateTime.now());
        userMapper.updateById(user);

        // 记录交易流水
        Wallet walletRecord = new Wallet();
        walletRecord.setUserId(userId);
        walletRecord.setTransactionType(4); // 提现
        walletRecord.setAmount(amount);
        walletRecord.setBalanceBefore(currentBalance);
        walletRecord.setBalanceAfter(newBalance);
        walletRecord.setDescription("账户提现");
        walletRecord.setTransactionNo(generateTransactionNo());
        walletRecord.setStatus(2); // 处理中
        walletRecord.setCreateTime(LocalDateTime.now());
        
        walletMapper.insert(walletRecord);
    }

    /**
     * 支付订单
     */
    @Transactional
    public void payOrder(Long userId, Long orderId, BigDecimal amount, String description) {
        SysUser user = userMapper.selectById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        BigDecimal currentBalance = user.getBalance() != null ? user.getBalance() : BigDecimal.ZERO;
        if (currentBalance.compareTo(amount) < 0) {
            throw new RuntimeException("余额不足");
        }

        BigDecimal newBalance = currentBalance.subtract(amount);

        // 更新用户余额
        user.setBalance(newBalance);
        user.setUpdateTime(LocalDateTime.now());
        userMapper.updateById(user);

        // 记录交易流水
        Wallet walletRecord = new Wallet();
        walletRecord.setUserId(userId);
        walletRecord.setOrderId(orderId);
        walletRecord.setTransactionType(2); // 消费
        walletRecord.setAmount(amount);
        walletRecord.setBalanceBefore(currentBalance);
        walletRecord.setBalanceAfter(newBalance);
        walletRecord.setDescription(description);
        walletRecord.setTransactionNo(generateTransactionNo());
        walletRecord.setStatus(1); // 成功
        walletRecord.setCreateTime(LocalDateTime.now());
        
        walletMapper.insert(walletRecord);
    }

    /**
     * 收入到账（代取员完成订单）
     */
    @Transactional
    public void receiveIncome(Long userId, Long orderId, BigDecimal amount, String description) {
        SysUser user = userMapper.selectById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        BigDecimal currentBalance = user.getBalance() != null ? user.getBalance() : BigDecimal.ZERO;
        BigDecimal newBalance = currentBalance.add(amount);

        // 更新用户余额
        user.setBalance(newBalance);
        user.setUpdateTime(LocalDateTime.now());
        userMapper.updateById(user);

        // 记录交易流水
        Wallet walletRecord = new Wallet();
        walletRecord.setUserId(userId);
        walletRecord.setOrderId(orderId);
        walletRecord.setTransactionType(3); // 收入
        walletRecord.setAmount(amount);
        walletRecord.setBalanceBefore(currentBalance);
        walletRecord.setBalanceAfter(newBalance);
        walletRecord.setDescription(description);
        walletRecord.setTransactionNo(generateTransactionNo());
        walletRecord.setStatus(1); // 成功
        walletRecord.setCreateTime(LocalDateTime.now());
        
        walletMapper.insert(walletRecord);
    }

    /**
     * 生成交易流水号
     */
    private String generateTransactionNo() {
        return "TXN" + System.currentTimeMillis() + IdUtils.randomUUID().substring(0, 8).toUpperCase();
    }
}