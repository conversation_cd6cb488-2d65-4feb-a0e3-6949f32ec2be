package com.example.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class UserInfoVo {
    private Long userId;
    private String username;
    private String realName;
    private String phone;
    private String email;
    private String studentId;
    private String avatar;
    private String userType; // 1-普通用户，2-代取员，3-管理员
    private Integer status;
    private Integer auditStatus;
    private String loginIp;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime loginDate;
}
