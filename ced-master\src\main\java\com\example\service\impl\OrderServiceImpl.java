package com.example.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.example.config.LoginUserContext;
import com.example.dto.OrderCreateDTO;
import com.example.entity.Address;
import com.example.entity.ExpressStation;
import com.example.entity.Order;
import com.example.entity.SysUser;
import com.example.mapper.AddressMapper;
import com.example.mapper.ExpressStationMapper;
import com.example.mapper.OrderMapper;
import com.example.mapper.UserMapper;
import com.example.service.OrderService;
import com.example.util.R;
import com.example.vo.OrderVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

@Service
public class OrderServiceImpl implements OrderService {
    
    @Autowired
    private OrderMapper orderMapper;
    
    @Autowired
    private UserMapper userMapper;
    
    @Autowired
    private AddressMapper addressMapper;
    
    @Autowired
    private ExpressStationMapper expressStationMapper;
    
    @Override
    @Transactional
    public R<String> createOrder(OrderCreateDTO orderCreateDTO) {
        Long userId = LoginUserContext.getUserInfo().getUserId();
        
        // 验证地址是否属于当前用户
        Address address = addressMapper.selectById(orderCreateDTO.getAddressId());
        if (address == null || !address.getUserId().equals(userId)) {
            return R.fail("收货地址不存在");
        }
        
        // 验证快递站点
        ExpressStation station = expressStationMapper.selectById(orderCreateDTO.getStationId());
        if (station == null || station.getStatus() != 1) {
            return R.fail("快递站点不可用");
        }
        
        // 创建订单
        Order order = new Order();
        BeanUtils.copyProperties(orderCreateDTO, order);
        order.setUserId(userId);
        order.setOrderNo(generateOrderNo());
        order.setDeliveryAddress(buildDeliveryAddress(address));
        order.setContactName(address.getContactName());
        order.setContactPhone(address.getContactPhone());
        order.setDeposit(orderCreateDTO.getServiceFee().multiply(new BigDecimal("0.3"))); // 30%定金
        order.setTotalAmount(orderCreateDTO.getServiceFee());
        order.setOrderStatus(1); // 待接单
        order.setPayStatus(1); // 待支付定金
        order.setCreateTime(LocalDateTime.now());
        order.setUpdateTime(LocalDateTime.now());
        
        orderMapper.insert(order);
        
        return R.ok("订单创建成功");
    }
    
    @Override
    @Transactional
    public R<String> acceptOrder(Long orderId) {
        Long pickupUserId = LoginUserContext.getUserInfo().getUserId();
        
        // 验证用户是否为代取员
        SysUser user = userMapper.selectById(pickupUserId);
        /*if (user.getUserType() != 2 || user.getAuditStatus() != 1) {
            return R.fail("您不是认证的代取员");
        }*/
        
        Order order = orderMapper.selectById(orderId);
        if (order == null) {
            return R.fail("订单不存在");
        }
        
        if (order.getOrderStatus() != 1) {
            return R.fail("订单状态不正确");
        }
        
        // 更新订单状态
        order.setPickupUserId(pickupUserId);
        order.setOrderStatus(2); // 已接单
        order.setUpdateTime(LocalDateTime.now());
        
        orderMapper.updateById(order);
        
        return R.ok("接单成功");
    }
    
    @Override
    public R<String> cancelOrder(Long orderId, String reason) {
        Order order = orderMapper.selectById(orderId);
        if (order == null) {
            return R.fail("订单不存在");
        }
        
        // 只有待接单状态才能取消
        if (order.getOrderStatus() != 1) {
            return R.fail("订单状态不允许取消");
        }
        
        order.setOrderStatus(7); // 已取消
        order.setCancelReason(reason);
        order.setUpdateTime(LocalDateTime.now());
        
        orderMapper.updateById(order);
        
        return R.ok("订单取消成功");
    }
    
    @Override
    public R<String> confirmPickup(Long orderId) {
        Order order = orderMapper.selectById(orderId);
        if (order == null) {
            return R.fail("订单不存在");
        }
        
        if (order.getOrderStatus() != 2) {
            return R.fail("订单状态不正确");
        }
        
        order.setOrderStatus(4); // 配送中
        order.setPickupTime(LocalDateTime.now());
        order.setUpdateTime(LocalDateTime.now());
        
        orderMapper.updateById(order);
        
        return R.ok("确认取件成功");
    }
    
    @Override
    public R<String> confirmDelivery(Long orderId) {
        Order order = orderMapper.selectById(orderId);
        if (order == null) {
            return R.fail("订单不存在");
        }
        
        if (order.getOrderStatus() != 4) {
            return R.fail("订单状态不正确");
        }
        
        order.setOrderStatus(5); // 待确认
        order.setDeliveryTime(LocalDateTime.now());
        order.setUpdateTime(LocalDateTime.now());
        
        orderMapper.updateById(order);
        
        return R.ok("确认送达成功");
    }
    
    @Override
    public R<String> confirmReceive(Long orderId) {
        Order order = orderMapper.selectById(orderId);
        if (order == null) {
            return R.fail("订单不存在");
        }
        
        if (order.getOrderStatus() != 5) {
            return R.fail("订单状态不正确");
        }
        
        order.setOrderStatus(6); // 已完成
        order.setPayStatus(4); // 已支付完成
        order.setUpdateTime(LocalDateTime.now());
        
        orderMapper.updateById(order);
        
        return R.ok("确认收货成功");
    }
    
    @Override
    public R<List<OrderVO>> getUserOrders(Long userId) {
        List<Order> orders = orderMapper.findByUserId(userId);
        List<OrderVO> orderVOs = convertToOrderVOs(orders);
        return R.ok(orderVOs);
    }
    
    @Override
    public R<List<OrderVO>> getPickupUserOrders(Long pickupUserId) {
        List<Order> orders = orderMapper.findByPickupUserId(pickupUserId);
        List<OrderVO> orderVOs = convertToOrderVOs(orders);
        return R.ok(orderVOs);
    }
    
    @Override
    public R<List<OrderVO>> getPendingOrders() {
        List<Order> orders = orderMapper.findPendingOrders();
        List<OrderVO> orderVOs = convertToOrderVOs(orders);
        return R.ok(orderVOs);
    }
    
    @Override
    public R<OrderVO> getOrderDetail(Long orderId) {
        Order order = orderMapper.selectById(orderId);
        if (order == null) {
            return R.fail("订单不存在");
        }
        
        OrderVO orderVO = convertToOrderVO(order);
        return R.ok(orderVO);
    }
    
    private String generateOrderNo() {
        return "EXP" + System.currentTimeMillis();
    }
    
    private String buildDeliveryAddress(Address address) {
        return address.getCampus() + " " + address.getBuilding() + " " + address.getRoom() + 
               (address.getDetailAddress() != null ? " " + address.getDetailAddress() : "");
    }
    
    private List<OrderVO> convertToOrderVOs(List<Order> orders) {
        List<OrderVO> orderVOs = new ArrayList<>();
        for (Order order : orders) {
            orderVOs.add(convertToOrderVO(order));
        }
        return orderVOs;
    }
    
    private OrderVO convertToOrderVO(Order order) {
        OrderVO orderVO = new OrderVO();
        BeanUtils.copyProperties(order, orderVO);
        
        // 设置状态文本
        orderVO.setOrderStatusText(getOrderStatusText(order.getOrderStatus()));
        orderVO.setPayStatusText(getPayStatusText(order.getPayStatus()));
        
        // 设置用户信息
        if (order.getUserId() != null) {
            SysUser user = userMapper.selectById(order.getUserId());
            if (user != null) {
                orderVO.setUserName(user.getRealName());
            }
        }
        
        // 设置代取员信息
        if (order.getPickupUserId() != null) {
            SysUser pickupUser = userMapper.selectById(order.getPickupUserId());
            if (pickupUser != null) {
                orderVO.setPickupUserName(pickupUser.getRealName());
                orderVO.setPickupUserPhone(pickupUser.getPhone());
            }
        }
        
        // 设置站点信息
        if (order.getStationId() != null) {
            ExpressStation station = expressStationMapper.selectById(order.getStationId());
            if (station != null) {
                orderVO.setStationName(station.getStationName());
            }
        }
        
        return orderVO;
    }
    
    private String getOrderStatusText(Integer status) {
        switch (status) {
            case 1: return "待接单";
            case 2: return "已接单";
            case 3: return "取件中";
            case 4: return "配送中";
            case 5: return "待确认";
            case 6: return "已完成";
            case 7: return "已取消";
            default: return "未知状态";
        }
    }
    
    private String getPayStatusText(Integer status) {
        switch (status) {
            case 1: return "待支付定金";
            case 2: return "已支付定金";
            case 3: return "待支付尾款";
            case 4: return "已支付完成";
            default: return "未知状态";
        }
    }
}