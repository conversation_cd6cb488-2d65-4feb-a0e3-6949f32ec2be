package com.example.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 公告视图对象
 */
@Data
public class NoticeVO {
    
    private Long id;
    
    private String title;
    
    private String content;
    
    private String type;
    
    private String target;
    
    private String status;
    
    private Integer readCount;
    
    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    private LocalDateTime publishTime;

    private Boolean isRead; // 用户是否已读（仅用户端使用）
}