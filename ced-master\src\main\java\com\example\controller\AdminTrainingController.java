package com.example.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.entity.CourierTraining;
import com.example.entity.CourierTrainingRecord;
import com.example.mapper.CourierTrainingMapper;
import com.example.mapper.CourierTrainingRecordMapper;
import com.example.util.R;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 管理员培训管理控制器
 */
@RestController
@RequestMapping("/admin/training")
public class AdminTrainingController {
    
    @Autowired
    private CourierTrainingMapper trainingMapper;
    
    @Autowired
    private CourierTrainingRecordMapper trainingRecordMapper;
    
    /**
     * 获取培训课程列表
     */
    @GetMapping
    public R<Map<String, Object>> getTrainingList(
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) Integer trainingType,
            @RequestParam(required = false) Integer status) {
        
        Page<CourierTraining> page = new Page<>(current, size);
        LambdaQueryWrapper<CourierTraining> query = new LambdaQueryWrapper<>();
        
        // 关键词搜索
        if (keyword != null && !keyword.isEmpty()) {
            query.like(CourierTraining::getTrainingTitle, keyword)
                 .or()
                 .like(CourierTraining::getTrainingContent, keyword);
        }
        
        // 培训类型筛选
        if (trainingType != null) {
            query.eq(CourierTraining::getTrainingType, trainingType);
        }
        
        // 状态筛选
        if (status != null) {
            query.eq(CourierTraining::getStatus, status);
        }
        
        query.orderByDesc(CourierTraining::getCreateTime);
        
        IPage<CourierTraining> trainingPage = trainingMapper.selectPage(page, query);
        
        Map<String, Object> result = new HashMap<>();
        result.put("records", trainingPage.getRecords());
        result.put("total", trainingPage.getTotal());
        result.put("current", trainingPage.getCurrent());
        result.put("size", trainingPage.getSize());
        
        return R.ok(result);
    }
    
    /**
     * 创建培训课程
     */
    @PostMapping
    public R<String> createTraining(@RequestBody CourierTraining training) {
        training.setCreateTime(LocalDateTime.now());
        training.setUpdateTime(LocalDateTime.now());
        training.setStatus(1); // 默认启用
        
        trainingMapper.insert(training);
        return R.ok("培训课程创建成功");
    }
    
    /**
     * 更新培训课程
     */
    @PutMapping("/{trainingId}")
    public R<String> updateTraining(
            @PathVariable Long trainingId,
            @RequestBody CourierTraining training) {
        
        CourierTraining existingTraining = trainingMapper.selectById(trainingId);
        if (existingTraining == null) {
            return R.fail("培训课程不存在");
        }
        
        training.setId(trainingId);
        training.setUpdateTime(LocalDateTime.now());
        trainingMapper.updateById(training);
        
        return R.ok("培训课程更新成功");
    }
    
    /**
     * 删除培训课程
     */
    @DeleteMapping("/{trainingId}")
    public R<String> deleteTraining(@PathVariable Long trainingId) {
        CourierTraining training = trainingMapper.selectById(trainingId);
        if (training == null) {
            return R.fail("培训课程不存在");
        }
        
        // 检查是否有培训记录
        LambdaQueryWrapper<CourierTrainingRecord> recordQuery = new LambdaQueryWrapper<>();
        recordQuery.eq(CourierTrainingRecord::getTrainingId, trainingId);
        Integer recordCount = trainingRecordMapper.selectCount(recordQuery);
        
        if (recordCount > 0) {
            return R.fail("该培训课程已有学习记录，无法删除");
        }
        
        trainingMapper.deleteById(trainingId);
        return R.ok("培训课程删除成功");
    }
    
    /**
     * 获取培训记录列表
     */
    @GetMapping("/records")
    public R<Map<String, Object>> getTrainingRecords(
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) Long courierId,
            @RequestParam(required = false) Long trainingId,
            @RequestParam(required = false) Integer status) {
        
        Page<CourierTrainingRecord> page = new Page<>(current, size);
        LambdaQueryWrapper<CourierTrainingRecord> query = new LambdaQueryWrapper<>();
        
        if (courierId != null) {
            query.eq(CourierTrainingRecord::getCourierId, courierId);
        }
        
        if (trainingId != null) {
            query.eq(CourierTrainingRecord::getTrainingId, trainingId);
        }
        
        if (status != null) {
            query.eq(CourierTrainingRecord::getStatus, status);
        }
        
        query.orderByDesc(CourierTrainingRecord::getCreateTime);
        
        IPage<CourierTrainingRecord> recordPage = trainingRecordMapper.selectPage(page, query);
        
        Map<String, Object> result = new HashMap<>();
        result.put("records", recordPage.getRecords());
        result.put("total", recordPage.getTotal());
        result.put("current", recordPage.getCurrent());
        result.put("size", recordPage.getSize());
        
        return R.ok(result);
    }
    
    /**
     * 获取培训统计数据
     */
    @GetMapping("/statistics")
    public R<Map<String, Object>> getTrainingStatistics() {
        Map<String, Object> statistics = new HashMap<>();
        
        // 总培训课程数
        Integer totalTrainings = trainingMapper.selectCount(null);
        statistics.put("totalTrainings", totalTrainings);
        
        // 启用的培训课程数
        LambdaQueryWrapper<CourierTraining> activeQuery = new LambdaQueryWrapper<>();
        activeQuery.eq(CourierTraining::getStatus, 1);
        Integer activeTrainings = trainingMapper.selectCount(activeQuery);
        statistics.put("activeTrainings", activeTrainings);
        
        // 总培训记录数
        Integer totalRecords = trainingRecordMapper.selectCount(null);
        statistics.put("totalRecords", totalRecords);
        
        // 已完成的培训记录数
        LambdaQueryWrapper<CourierTrainingRecord> completedQuery = new LambdaQueryWrapper<>();
        completedQuery.eq(CourierTrainingRecord::getStatus, 2);
        Integer completedRecords = trainingRecordMapper.selectCount(completedQuery);
        statistics.put("completedRecords", completedRecords);
        
        // 计算完成率
        double completionRate = totalRecords > 0 ? (double) completedRecords / totalRecords * 100 : 0;
        statistics.put("completionRate", Math.round(completionRate * 100.0) / 100.0);
        
        return R.ok(statistics);
    }
}
