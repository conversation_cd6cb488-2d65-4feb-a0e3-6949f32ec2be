package com.example.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 代取员培训记录实体类
 */
@Data
@TableName("courier_training_record")
public class CourierTrainingRecord {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    @TableField("courier_id")
    private Long courierId; // 代取员ID
    
    @TableField("training_id")
    private Long trainingId; // 培训ID
    
    @TableField("start_time")
    private LocalDateTime startTime; // 开始时间
    
    @TableField("end_time")
    private LocalDateTime endTime; // 结束时间
    
    @TableField("study_duration")
    private Integer studyDuration; // 学习时长（分钟）
    
    @TableField("exam_score")
    private Integer examScore; // 考试分数
    
    @TableField("is_passed")
    private Boolean isPassed; // 是否通过
    
    @TableField("completion_rate")
    private Integer completionRate; // 完成率（百分比）
    
    private Integer status; // 状态：1-进行中，2-已完成，3-已过期
    
    @TableField("certificate_url")
    private String certificateUrl; // 证书URL
    
    @TableField("notes")
    private String notes; // 备注
    
    @TableField("create_time")
    private LocalDateTime createTime;
    
    @TableField("update_time")
    private LocalDateTime updateTime;
}
