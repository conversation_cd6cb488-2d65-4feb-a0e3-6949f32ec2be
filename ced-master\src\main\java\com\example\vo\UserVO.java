package com.example.vo;

import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 用户VO
 */
@Data
public class UserVO {
    private Long userId;
    private String username;
    private String phone;
    private String studentId;
    private String realName;
    private String avatar;
    private String email;
    private Integer userType;
    private String userTypeText;
    private Integer status;
    
    // 代取员相关信息
    private String alipayAccount;
    private String wechatAccount;
    private Integer auditStatus;
    private String auditStatusText;
    private BigDecimal balance;
    private BigDecimal rating;
    private Integer orderCount;
    
    private LocalDateTime createTime;
}