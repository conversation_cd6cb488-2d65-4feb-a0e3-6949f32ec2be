package com.example.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 订单评价实体类 - 对应数据库order_evaluation表
 */
@Data
@TableName("order_evaluation")
public class OrderEvaluation {
    @TableId(type = IdType.AUTO)
    @TableField("evaluation_id")
    private Long evaluationId;
    
    @TableField("order_id")
    private Long orderId; // 订单ID
    
    @TableField("user_id")
    private Long userId; // 评价用户ID
    
    @TableField("pickup_user_id")
    private Long pickupUserId; // 被评价代取员ID
    
    private Integer rating; // 评分：1-5星
    private String comment; // 评价内容
    private String images; // 评价图片，多张用逗号分隔
    
    @TableField("evaluation_type")
    private Integer evaluationType; // 评价类型：1-服务态度，2-配送速度，3-整体满意度
    
    @TableField("create_time")
    private LocalDateTime createTime;
}