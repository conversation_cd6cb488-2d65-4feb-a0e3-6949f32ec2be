package com.example.controller;

import com.example.annotation.HasRole;
import com.example.config.LoginUserContext;
import com.example.dto.UserUpdateDTO;
import com.example.dto.PasswordUpdateDTO;
import com.example.entity.SysUser;
import com.example.service.SysUserService;
import com.example.service.AddressService;
import com.example.util.R;
import com.example.vo.UserProfileVO;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

@RestController
@RequestMapping("/user")
public class UserController {

    @Resource
    private SysUserService sysUserService;
    
    @Resource
    private AddressService addressService;

    /**
     * 获取当前用户信息
     */
    @GetMapping("/getInfo")
    public R<?> getInfo() {
        return R.ok(LoginUserContext.getUserInfo());
    }

    /**
     * 获取用户详细资料
     */
    @GetMapping("/profile")
    public R<UserProfileVO> getUserProfile() {
        Long userId = LoginUserContext.getUserInfo().getUserId();
        UserProfileVO profile = sysUserService.getUserProfile(userId);
        return R.ok(profile);
    }

    /**
     * 更新用户基本信息
     */
    @PutMapping("/profile")
    public R<?> updateUserProfile(@RequestBody @Validated UserUpdateDTO userUpdateDTO) {
        Long userId = LoginUserContext.getUserInfo().getUserId();
        sysUserService.updateUserProfile(userId, userUpdateDTO);
        return R.ok("个人信息更新成功");
    }

    /**
     * 修改密码
     */
    @PutMapping("/password")
    public R<?> updatePassword(@RequestBody @Validated PasswordUpdateDTO passwordUpdateDTO) {
        Long userId = LoginUserContext.getUserInfo().getUserId();
        sysUserService.updatePassword(userId, passwordUpdateDTO);
        return R.ok("密码修改成功");
    }

    /**
     * 上传头像
     */
    @PostMapping("/avatar")
    public R<?> uploadAvatar(@RequestParam("file") MultipartFile file) {
        Long userId = LoginUserContext.getUserInfo().getUserId();
        String avatarUrl = sysUserService.uploadAvatar(userId, file);
        return R.ok("头像上传成功", avatarUrl);
    }

    /**
     * 删除头像
     */
    @DeleteMapping("/avatar")
    public R<?> deleteAvatar() {
        Long userId = LoginUserContext.getUserInfo().getUserId();
        sysUserService.deleteAvatar(userId);
        return R.ok("头像删除成功");
    }

    /**
     * 获取用户统计信息
     */
    @GetMapping("/statistics")
    public R<?> getUserStatistics() {
        Long userId = LoginUserContext.getUserInfo().getUserId();
        return R.ok(sysUserService.getUserStatistics(userId));
    }

    /**
     * 获取用户地址列表
     */
    @GetMapping("/addresses")
    public R<?> getUserAddresses() {
        Long userId = LoginUserContext.getUserInfo().getUserId();
        return R.ok(addressService.getUserAddresses(userId));
    }

    /**
     * 获取代取员接单设置
     */
    @HasRole("pickup")
    @GetMapping("/pickup-settings")
    public R<?> getPickupSettings() {
        Long userId = LoginUserContext.getUserInfo().getUserId();
        return R.ok(sysUserService.getPickupSettings(userId));
    }

    /**
     * 更新代取员接单设置
     */
    @HasRole("pickup")
    @PutMapping("/pickup-settings")
    public R<?> updatePickupSettings(@RequestBody @Validated Object pickupSettings) {
        Long userId = LoginUserContext.getUserInfo().getUserId();
        sysUserService.updatePickupSettings(userId, pickupSettings);
        return R.ok("接单设置更新成功");
    }

    // 测试管理员权限接口
    @HasRole("admin")
    @GetMapping("/getAdmin")
    public R<?> getAdmin() {
        return R.ok();
    }
    
    // 测试普通用户权限接口
    @HasRole("common")
    @GetMapping("/getCommon")
    public R<?> getCommon() {
        return R.ok();
    }
    
    // 测试管理员和普通用户权限接口
    @HasRole("admin,common")
    @GetMapping("/getAdminCommon")
    public R<?> getAdminCommon() {
        return R.ok();
    }
}
