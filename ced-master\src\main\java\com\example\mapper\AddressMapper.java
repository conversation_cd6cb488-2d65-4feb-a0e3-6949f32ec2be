package com.example.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.entity.Address;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import java.util.List;

@Mapper
public interface AddressMapper extends BaseMapper<Address> {
    
    /**
     * 查询用户的地址列表
     */
    @Select("SELECT * FROM address WHERE user_id = #{userId} ORDER BY is_default DESC, create_time DESC")
    List<Address> findByUserId(@Param("userId") Long userId);
    
    /**
     * 查询用户的默认地址
     */
    @Select("SELECT * FROM address WHERE user_id = #{userId} AND is_default = 1")
    Address findDefaultByUserId(@Param("userId") Long userId);
}