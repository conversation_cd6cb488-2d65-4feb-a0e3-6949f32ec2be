package com.example.dto;

import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 创建订单DTO
 */
@Data
public class OrderCreateDTO {
    
    @NotNull(message = "快递站点不能为空")
    private Long stationId;
    
    @NotBlank(message = "取件码不能为空")
    private String pickupCode;
    
    private String trackingNumber;
    
    private String expressDescription;
    
    @NotNull(message = "收货地址不能为空")
    private Long addressId;
    
    private LocalDateTime expectedTime;
    
    @NotNull(message = "代取费用不能为空")
    private BigDecimal serviceFee;
    
    private String remark;
}