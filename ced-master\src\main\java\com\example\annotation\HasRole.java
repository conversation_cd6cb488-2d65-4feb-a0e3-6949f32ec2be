package com.example.annotation;

import com.example.enums.LogicalType;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 角色注解
 * 支持多角色验证，角色之间用逗号分隔
 * 例如：@HasRole("admin,common")
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface HasRole {
    /**
     * 需要校验的角色标识
     * 多个角色标识使用逗号分隔
     */
    String value();
    
    /**
     * 验证模式：ANY任意一个角色通过即可，ALL必须全部角色同时具有
     */
    LogicalType logical() default LogicalType.ANY;
}