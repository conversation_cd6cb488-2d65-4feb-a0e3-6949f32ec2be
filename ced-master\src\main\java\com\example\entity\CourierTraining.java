package com.example.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 代取员培训实体类
 */
@Data
@TableName("courier_training")
public class CourierTraining {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    @TableField("training_title")
    private String trainingTitle; // 培训标题
    
    @TableField("training_content")
    private String trainingContent; // 培训内容
    
    @TableField("training_type")
    private Integer trainingType; // 培训类型：1-入职培训，2-技能提升，3-安全培训，4-服务规范
    
    @TableField("training_duration")
    private Integer trainingDuration; // 培训时长（分钟）
    
    @TableField("required_level")
    private Integer requiredLevel; // 要求等级：1-初级，2-中级，3-高级，4-专家
    
    @TableField("is_mandatory")
    private Boolean isMandatory; // 是否必修
    
    @TableField("pass_score")
    private Integer passScore; // 及格分数
    
    @TableField("training_materials")
    private String trainingMaterials; // 培训材料（JSON格式存储文件列表）
    
    @TableField("video_url")
    private String videoUrl; // 培训视频URL
    
    private Integer status; // 状态：1-启用，0-禁用
    
    @TableField("create_time")
    private LocalDateTime createTime;
    
    @TableField("update_time")
    private LocalDateTime updateTime;
    
    @TableField("created_by")
    private Long createdBy; // 创建人ID
}
