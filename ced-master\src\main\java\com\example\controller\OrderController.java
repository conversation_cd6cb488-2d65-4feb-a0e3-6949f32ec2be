package com.example.controller;

import com.example.dto.OrderCreateDTO;
import com.example.service.OrderService;
import com.example.util.R;
import com.example.vo.OrderVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 订单控制器
 */
@RestController
@RequestMapping("/order")
public class OrderController {
    
    @Autowired
    private OrderService orderService;
    
    /**
     * 创建订单
     */
    @PostMapping("/create")
    public R<String> createOrder(@Valid @RequestBody OrderCreateDTO orderCreateDTO) {
        return orderService.createOrder(orderCreateDTO);
    }
    
    /**
     * 接单
     */
    @PostMapping("/accept/{orderId}")
    public R<String> acceptOrder(@PathVariable Long orderId) {
        return orderService.acceptOrder(orderId);
    }
    
    /**
     * 取消订单
     */
    @PostMapping("/cancel/{orderId}")
    public R<String> cancelOrder(@PathVariable Long orderId, @RequestParam String reason) {
        return orderService.cancelOrder(orderId, reason);
    }
    
    /**
     * 确认取件
     */
    @PostMapping("/confirm-pickup/{orderId}")
    public R<String> confirmPickup(@PathVariable Long orderId) {
        return orderService.confirmPickup(orderId);
    }
    
    /**
     * 确认送达
     */
    @PostMapping("/confirm-delivery/{orderId}")
    public R<String> confirmDelivery(@PathVariable Long orderId) {
        return orderService.confirmDelivery(orderId);
    }
    
    /**
     * 用户确认收货
     */
    @PostMapping("/confirm-receive/{orderId}")
    public R<String> confirmReceive(@PathVariable Long orderId) {
        return orderService.confirmReceive(orderId);
    }
    
    /**
     * 查询我的订单（用户端）
     */
    @GetMapping("/my-orders")
    public R<List<OrderVO>> getMyOrders() {
        // 这里需要从登录上下文获取用户ID
        Long userId = 1L; // 临时写死，实际应该从LoginUserContext获取
        return orderService.getUserOrders(userId);
    }
    
    /**
     * 查询我接的订单（代取员端）
     */
    @GetMapping("/pickup-orders")
    public R<List<OrderVO>> getPickupOrders() {
        // 这里需要从登录上下文获取用户ID
        Long pickupUserId = 1L; // 临时写死，实际应该从LoginUserContext获取
        return orderService.getPickupUserOrders(pickupUserId);
    }
    
    /**
     * 查询待接单列表
     */
    @GetMapping("/pending")
    public R<List<OrderVO>> getPendingOrders() {
        return orderService.getPendingOrders();
    }
    
    /**
     * 查询订单详情
     */
    @GetMapping("/{orderId}")
    public R<OrderVO> getOrderDetail(@PathVariable Long orderId) {
        return orderService.getOrderDetail(orderId);
    }
}