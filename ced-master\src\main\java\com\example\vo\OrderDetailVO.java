package com.example.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 订单详情视图对象
 */
@Data
public class OrderDetailVO {
    
    private Long orderId;
    
    private Long userId;
    
    private String userName;
    
    private String contactPhone;
    
    private Long expressPointId;
    
    private String expressPointName;
    
    private String pickupCode;
    
    private String campus;
    
    private String dormBuilding;
    
    private String roomNumber;
    
    private String deliveryAddress;
    
    private BigDecimal fee;
    
    private Long courierId;
    
    private String courierName;
    
    private String courierPhone;
    
    private Integer orderStatus;
    
    private String description;
    
    private String specialRequirements;
    
    private String cancelReason;
    
    private String note;
    
    private LocalDateTime expectedTime;
    
    private LocalDateTime createTime;
    
    private LocalDateTime updateTime;
    
    private List<OrderTimelineVO> timeline;
}