package com.example.handler;

import com.example.util.HttpStatus;
import com.example.util.R;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.util.stream.Collectors;

@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    // 处理参数校验异常（如@Valid失败）
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public R<?> handleValidationException(MethodArgumentNotValidException ex) {
        String errorMsg = ex.getBindingResult().getFieldErrors().stream()
                .map(DefaultMessageSourceResolvable::getDefaultMessage)
                .collect(Collectors.joining(", "));
        return R.fail(HttpStatus.BAD_REQUEST, errorMsg);
    }

    // 处理抛出异常
    @ExceptionHandler(RuntimeException.class)
    public R<?> handleRuntimeException(RuntimeException ex) {
        log.error("参数校验失败: ", ex);
        return R.fail(HttpStatus.BAD_REQUEST, ex.getMessage());
    }

    // 兜底处理其他未知异常
    @ExceptionHandler(Exception.class)
    public R<?> handleGenericException(Exception ex) {
        log.error("系统异常: ", ex);
        return R.fail("500", "系统繁忙，请稍后再试");
    }
}