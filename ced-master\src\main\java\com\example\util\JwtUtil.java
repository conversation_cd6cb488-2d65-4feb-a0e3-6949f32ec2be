package com.example.util;
import com.example.vo.UserInfoVo;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;

import java.util.Date;

public class JwtUtil {
    // Token密钥
    private static final String SECRET_KEY = "your-secret-key";
    // Token过期时间（24小时）
    private static final long EXPIRATION_TIME = 86400000;
    // ObjectMapper实例
    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 生成JWT Token
     * @param userInfoVo 用户信息
     * @return token字符串
     */
    public static String generateToken(UserInfoVo userInfoVo) {
        try {
            Date now = new Date();
            Date expiryDate = new Date(now.getTime() + EXPIRATION_TIME);
            String userJson = objectMapper.writeValueAsString(userInfoVo);
            return Jwts.builder()
                    .setSubject(userJson)
                    .setIssuedAt(now)
                    .setExpiration(expiryDate)
                    .signWith(SignatureAlgorithm.HS512, SECRET_KEY)
                    .compact();
        } catch (Exception e) {
            throw new RuntimeException("生成Token失败", e);
        }
    }

    public static UserInfoVo getUserInfoFromToken(String token) {
        try {
            Claims claims = Jwts.parser()
                    .setSigningKey(SECRET_KEY)
                    .parseClaimsJws(token)
                    .getBody();
            String userJson = claims.getSubject();
            return objectMapper.readValue(userJson, UserInfoVo.class);
        } catch (Exception e) {
            throw new RuntimeException("解析Token失败", e);
        }
    }

    /**
     * 从Token中获取用户信息
     * @param token JWT Token
     * @return 用户信息对象
     */
    public static Long getUserIdFromToken(String token) {
        try {
            Claims claims = Jwts.parser()
                    .setSigningKey(SECRET_KEY)
                    .parseClaimsJws(token)
                    .getBody();
            String userJson = claims.getSubject();
            return objectMapper.readValue(userJson, UserInfoVo.class).getUserId();
        } catch (Exception e) {
            throw new RuntimeException("解析Token失败", e);
        }
    }

    public static String getUsernameFromToken(String token) {
        try {
            Claims claims = Jwts.parser()
                    .setSigningKey(SECRET_KEY)
                    .parseClaimsJws(token)
                    .getBody();
            String userJson = claims.getSubject();
            return objectMapper.readValue(userJson, UserInfoVo.class).getUsername();
        } catch (Exception e) {
            throw new RuntimeException("解析Token失败", e);
        }
    }

    /**
     * 验证Token是否有效
     * @param token JWT Token
     * @return 是否有效
     */
    public static boolean validateToken(String token) {
        try {
            Jwts.parser().setSigningKey(SECRET_KEY).parseClaimsJws(token);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
}