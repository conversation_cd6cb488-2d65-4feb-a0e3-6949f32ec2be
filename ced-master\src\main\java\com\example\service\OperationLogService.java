package com.example.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.entity.OperationLog;
import com.example.mapper.OperationLogMapper;
import com.example.vo.OperationLogVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 操作日志服务类
 */
@Slf4j
@Service
public class OperationLogService {
    
    @Autowired
    private OperationLogMapper operationLogMapper;
    
    /**
     * 记录操作日志
     */
    public void recordLog(Long operatorId, String operatorName, Integer operatorType,
                         String actionType, String actionName, String targetType, 
                         Long targetId, String targetName, String description,
                         String requestMethod, String requestUrl, String requestParams,
                         String responseResult, Integer status, String errorMessage,
                         Long executionTime) {
        try {
            OperationLog log = new OperationLog();
            log.setOperatorId(operatorId);
            log.setOperatorName(operatorName);
            log.setOperatorType(operatorType);
            log.setActionType(actionType);
            log.setActionName(actionName);
            log.setTargetType(targetType);
            log.setTargetId(targetId);
            log.setTargetName(targetName);
            log.setDescription(description);
            log.setRequestMethod(requestMethod);
            log.setRequestUrl(requestUrl);
            log.setRequestParams(requestParams);
            log.setResponseResult(responseResult);
            log.setStatus(status);
            log.setErrorMessage(errorMessage);
            log.setExecutionTime(executionTime);
            log.setCreateTime(LocalDateTime.now());
            
            // 获取IP地址和User-Agent
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                log.setIpAddress(getClientIpAddress(request));
                log.setUserAgent(request.getHeader("User-Agent"));
            }
            
            operationLogMapper.insert(log);
        } catch (Exception e) {
            log.error("记录操作日志失败", e);
        }
    }
    
    /**
     * 分页查询操作日志
     */
    public IPage<OperationLogVO> getOperationLogs(Integer current, Integer size,
                                                  String actionType, String operatorName,
                                                  LocalDateTime startTime, LocalDateTime endTime) {
        Page<OperationLog> page = new Page<>(current, size);

        // 使用LambdaQueryWrapper构建查询条件
        LambdaQueryWrapper<OperationLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(actionType != null && !actionType.trim().isEmpty(), OperationLog::getActionType, actionType)
                   .like(operatorName != null && !operatorName.trim().isEmpty(), OperationLog::getOperatorName, operatorName)
                   .ge(startTime != null, OperationLog::getCreateTime, startTime)
                   .le(endTime != null, OperationLog::getCreateTime, endTime != null ? endTime.plusDays(1) : null)
                   .orderByDesc(OperationLog::getCreateTime);

        IPage<OperationLog> logPage = operationLogMapper.selectPage(page, queryWrapper);

        // 转换为VO
        List<OperationLogVO> logVOs = logPage.getRecords().stream().map(this::convertToVO).collect(Collectors.toList());

        Page<OperationLogVO> voPage = new Page<>(current, size);
        voPage.setRecords(logVOs);
        voPage.setTotal(logPage.getTotal());
        voPage.setCurrent(logPage.getCurrent());
        voPage.setSize(logPage.getSize());

        return voPage;
    }
    
    /**
     * 获取操作统计数据
     */
    public List<Map<String, Object>> getActionStatistics(LocalDateTime startTime, LocalDateTime endTime) {
        // 使用LambdaQueryWrapper查询统计数据
        LambdaQueryWrapper<OperationLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.ge(startTime != null, OperationLog::getCreateTime, startTime)
                   .le(endTime != null, OperationLog::getCreateTime, endTime)
                   .groupBy(OperationLog::getActionType);

        List<OperationLog> logs = operationLogMapper.selectList(queryWrapper);

        // 手动统计数据
        Map<String, Long> countMap = logs.stream()
                .collect(Collectors.groupingBy(OperationLog::getActionType, Collectors.counting()));

        return countMap.entrySet().stream()
                .map(entry -> {
                    Map<String, Object> map = new HashMap<>();
                    map.put("action_type", entry.getKey());
                    map.put("count", entry.getValue());
                    return map;
                })
                .collect(Collectors.toList());
    }

    /**
     * 获取今日操作数量
     */
    public Integer getTodayOperationCount() {
        LocalDateTime startOfDay = LocalDate.now().atStartOfDay();
        LocalDateTime endOfDay = startOfDay.plusDays(1);

        LambdaQueryWrapper<OperationLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.ge(OperationLog::getCreateTime, startOfDay)
                   .lt(OperationLog::getCreateTime, endOfDay);

        return Math.toIntExact(operationLogMapper.selectCount(queryWrapper));
    }

    /**
     * 获取失败操作数量
     */
    public Integer getFailedOperationCount(LocalDateTime startTime) {
        LambdaQueryWrapper<OperationLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OperationLog::getStatus, 0)
                   .ge(startTime != null, OperationLog::getCreateTime, startTime);

        return Math.toIntExact(operationLogMapper.selectCount(queryWrapper));
    }

    /**
     * 清理过期日志
     */
    public Integer cleanExpiredLogs(LocalDateTime beforeTime) {
        LambdaQueryWrapper<OperationLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.lt(OperationLog::getCreateTime, beforeTime);

        return operationLogMapper.delete(queryWrapper);
    }
    
    /**
     * 转换为VO对象
     */
    private OperationLogVO convertToVO(OperationLog log) {
        OperationLogVO vo = new OperationLogVO();
        BeanUtils.copyProperties(log, vo);
        
        // 格式化时间
        vo.setTime(log.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        
        // 设置操作员类型文本
        vo.setOperatorType(getOperatorTypeText(log.getOperatorType()));
        
        // 设置操作类型（用于前端显示）
        vo.setAction(log.getActionType());
        
        // 设置操作对象
        vo.setTarget(log.getTargetName() != null ? log.getTargetName() : log.getTargetType());
        
        // 设置结果
        vo.setResult(log.getStatus() == 1 ? "成功" : "失败");
        
        return vo;
    }
    
    /**
     * 获取操作员类型文本
     */
    private String getOperatorTypeText(Integer operatorType) {
        if (operatorType == null) return "未知";
        switch (operatorType) {
            case 1: return "管理员";
            case 2: return "代取员";
            case 3: return "学生";
            default: return "未知";
        }
    }
    
    /**
     * 获取客户端IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0];
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }
}
