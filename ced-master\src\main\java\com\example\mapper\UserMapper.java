package com.example.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.entity.SysUser;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import java.util.List;

@Mapper
public interface UserMapper extends BaseMapper<SysUser> {
    
    /**
     * 根据学号查询用户
     */
    @Select("SELECT * FROM user WHERE student_id = #{studentId}")
    SysUser findByStudentId(@Param("studentId") String studentId);
    
    /**
     * 根据手机号查询用户
     */
    @Select("SELECT * FROM user WHERE phone = #{phone}")
    SysUser findByPhone(@Param("phone") String phone);
    
    /**
     * 查询可用的代取员列表
     */
    @Select("SELECT * FROM user WHERE user_type = 2 AND status = 1 AND audit_status = 1")
    List<SysUser> findAvailablePickupUsers();
    
    /**
     * 更新用户余额
     */
    int updateBalance(@Param("userId") Long userId, @Param("amount") java.math.BigDecimal amount);
}