package com.example.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户通知读取状态实体类
 */
@Data
@TableName("user_notification_read")
public class UserNotificationRead {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    @TableField("user_id")
    private Long userId; // 用户ID
    
    @TableField("notice_id")
    private Long noticeId; // 通知ID
    
    @TableField("read_time")
    private LocalDateTime readTime; // 读取时间
    
    @TableField("create_time")
    private LocalDateTime createTime; // 创建时间
}
