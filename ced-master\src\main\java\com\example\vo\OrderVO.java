package com.example.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 订单视图对象
 */
@Data
public class OrderVO {
    
    private Long orderId;
    
    private Long userId;
    
    private String userName;
    
    private String contactPhone;
    
    private Long expressPointId;
    
    private String expressPointName;
    
    private String pickupCode;
    
    private String campus;
    
    private String dormBuilding;
    
    private String roomNumber;
    
    private String deliveryAddress;
    
    private BigDecimal fee;
    
    private Long courierId;
    
    private String courierName;
    
    private Integer orderStatus;
    
    private String description;
    
    private String specialRequirements;
    
    private String cancelReason;
    
    private LocalDateTime expectedTime;
    
    private LocalDateTime createTime;
    
    private LocalDateTime updateTime;

    private String orderStatusText;

    private String payStatusText;
    private String pickupUserName;
    private String pickupUserPhone;
    private String stationName;
}