package com.example.dto;

import lombok.Data;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;

/**
 * 充值DTO
 */
@Data
public class RechargeDTO {
    
    @NotNull(message = "充值金额不能为空")
    @DecimalMin(value = "0.01", message = "充值金额必须大于0")
    private BigDecimal amount;
    
    @NotBlank(message = "支付方式不能为空")
    private String payMethod; // alipay, wechat
    
    private String remark; // 备注
}