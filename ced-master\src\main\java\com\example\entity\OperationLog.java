package com.example.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 操作日志实体类 - 对应数据库operation_log表
 */
@Data
@TableName("operation_log")
public class OperationLog {
    @TableId(type = IdType.AUTO)
    @TableField("log_id")
    private Long logId;
    
    @TableField("operator_id")
    private Long operatorId; // 操作员ID
    
    @TableField("operator_name")
    private String operatorName; // 操作员姓名
    
    @TableField("operator_type")
    private Integer operatorType; // 操作员类型：1-管理员，2-代取员，3-学生
    
    @TableField("action_type")
    private String actionType; // 操作类型：用户管理、订单管理、系统配置等
    
    @TableField("action_name")
    private String actionName; // 具体操作名称
    
    @TableField("target_type")
    private String targetType; // 操作对象类型：用户、订单、公告等
    
    @TableField("target_id")
    private Long targetId; // 操作对象ID
    
    @TableField("target_name")
    private String targetName; // 操作对象名称
    
    private String description; // 操作描述
    
    @TableField("request_method")
    private String requestMethod; // 请求方法：GET、POST、PUT、DELETE
    
    @TableField("request_url")
    private String requestUrl; // 请求URL
    
    @TableField("request_params")
    private String requestParams; // 请求参数
    
    @TableField("response_result")
    private String responseResult; // 响应结果
    
    @TableField("ip_address")
    private String ipAddress; // 操作IP地址
    
    @TableField("user_agent")
    private String userAgent; // 用户代理
    
    private Integer status; // 操作状态：1-成功，0-失败
    
    @TableField("error_message")
    private String errorMessage; // 错误信息
    
    @TableField("execution_time")
    private Long executionTime; // 执行时间（毫秒）
    
    @TableField("create_time")
    private LocalDateTime createTime;
}
