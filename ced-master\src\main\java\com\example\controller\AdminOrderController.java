package com.example.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.entity.Order;
import com.example.entity.SysUser;
import com.example.mapper.OrderMapper;
import com.example.mapper.UserMapper;
import com.example.util.R;
import com.example.vo.OrderDetailVO;
import com.example.vo.OrderVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 管理员订单管理控制器
 */
@RestController
@RequestMapping("/admin/orders")
public class AdminOrderController {
    
    @Autowired
    private OrderMapper orderMapper;
    
    @Autowired
    private UserMapper userMapper;
    
    /**
     * 获取订单列表（分页）
     */
    @GetMapping
    public R<Map<String, Object>> getOrders(
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) Integer status,
            @RequestParam(required = false) String campus,
            @RequestParam(required = false) Float minFee,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        
        Page<Order> page = new Page<>(current, size);
        QueryWrapper<Order> query = new QueryWrapper<>();
        
        // 关键词搜索
        if (keyword != null && !keyword.isEmpty()) {
            query.and(wrapper -> wrapper
                    .like("order_id", keyword)
                    .or()
                    .like("user_name", keyword)
                    .or()
                    .like("pickup_code", keyword));
        }
        
        // 状态筛选
        if (status != null) {
            query.eq("order_status", status);
        }
        
        // 校区筛选
        if (campus != null && !campus.isEmpty()) {
            query.eq("campus", campus);
        }
        
        // 费用筛选
        if (minFee != null) {
            query.ge("fee", minFee);
        }
        
        // 日期筛选
        if (startDate != null && !startDate.isEmpty()) {
            query.ge("create_time", LocalDate.parse(startDate).atStartOfDay());
        }
        if (endDate != null && !endDate.isEmpty()) {
            query.le("create_time", LocalDate.parse(endDate).plusDays(1).atStartOfDay());
        }
        
        // 按创建时间降序排序
        query.orderByDesc("create_time");
        
        IPage<Order> orderPage = orderMapper.selectPage(page, query);
        List<OrderVO> orderVOs = orderPage.getRecords().stream().map(order -> {
            OrderVO orderVO = new OrderVO();
            BeanUtils.copyProperties(order, orderVO);
            return orderVO;
        }).collect(Collectors.toList());
        
        Map<String, Object> result = new HashMap<>();
        result.put("records", orderVOs);
        result.put("total", orderPage.getTotal());
        result.put("pages", orderPage.getPages());
        result.put("current", orderPage.getCurrent());
        result.put("size", orderPage.getSize());
        
        return R.ok(result);
    }
    
    /**
     * 获取订单详情
     */
    @GetMapping("/{orderId}/detail")
    public R<OrderDetailVO> getOrderDetail(@PathVariable Long orderId) {
        Order order = orderMapper.selectById(orderId);
        if (order == null) {
            return R.fail("订单不存在");
        }
        
        OrderDetailVO detailVO = new OrderDetailVO();
        BeanUtils.copyProperties(order, detailVO);
        
        // 如果有代取员，获取代取员信息
        if (order.getCourierId() != null) {
            SysUser courier = userMapper.selectById(order.getCourierId());
            if (courier != null) {
                detailVO.setCourierName(courier.getRealName());
                detailVO.setCourierPhone(courier.getPhone());
            }
        }
        
        return R.ok(detailVO);
    }
    
    /**
     * 分配代取员
     */
    @PostMapping("/{orderId}/assign")
    public R<String> assignCourier(
            @PathVariable Long orderId,
            @RequestBody Map<String, Object> params) {
        
        Long courierId = Long.valueOf(params.get("courierId").toString());
        String note = (String) params.get("note");
        
        Order order = orderMapper.selectById(orderId);
        if (order == null) {
            return R.fail("订单不存在");
        }
        
        if (order.getOrderStatus() != 1) { // 假设1是待接单状态
            return R.fail("只能为待接单状态的订单分配代取员");
        }
        
        SysUser courier = userMapper.selectById(courierId);
        /*if (courier == null || courier.getUserType() != 2 || courier.getAuditStatus() != 1) {
            return R.fail("代取员不存在或不可用");
        }*/
        
        // 更新订单信息
        order.setCourierId(courierId);
        order.setCourierName(courier.getRealName());
        order.setOrderStatus(2); // 假设2是已接单状态
        order.setNote(note);
        order.setUpdateTime(LocalDateTime.now());
        orderMapper.updateById(order);
        
        // 更新代取员的订单数量
        courier.setOrderCount(courier.getOrderCount() + 1);
        userMapper.updateById(courier);
        
        return R.ok("代取员分配成功");
    }
    
    /**
     * 取消订单
     */
    @PostMapping("/{orderId}/cancel")
    public R<String> cancelOrder(
            @PathVariable Long orderId,
            @RequestBody Map<String, Object> params) {
        
        String reason = (String) params.get("reason");
        
        Order order = orderMapper.selectById(orderId);
        if (order == null) {
            return R.fail("订单不存在");
        }
        
        if (order.getOrderStatus() >= 4) { // 假设4及以上是已完成或已取消状态
            return R.fail("已完成或已取消的订单不能取消");
        }
        
        // 更新订单状态
        order.setOrderStatus(5); // 假设5是已取消状态
        order.setCancelReason(reason);
        order.setUpdateTime(LocalDateTime.now());
        orderMapper.updateById(order);
        
        return R.ok("订单取消成功");
    }
    
    /**
     * 删除订单
     */
    @DeleteMapping("/{orderId}")
    public R<String> deleteOrder(@PathVariable Long orderId) {
        Order order = orderMapper.selectById(orderId);
        if (order == null) {
            return R.fail("订单不存在");
        }
        
        orderMapper.deleteById(orderId);
        return R.ok("订单删除成功");
    }
    
    /**
     * 批量删除订单
     */
    @PostMapping("/batch-delete")
    public R<String> batchDeleteOrders(@RequestBody Map<String, Object> params) {
        List<Long> orderIds = (List<Long>) params.get("orderIds");
        
        if (orderIds == null || orderIds.isEmpty()) {
            return R.fail("订单ID列表不能为空");
        }
        
        orderMapper.deleteBatchIds(orderIds);
        return R.ok("批量删除成功");
    }
    
    /**
     * 导出订单数据
     */
    @GetMapping("/export")
    public ResponseEntity<byte[]> exportOrders(
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) Integer status,
            @RequestParam(required = false) String campus,
            @RequestParam(required = false) Float minFee,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        
        QueryWrapper<Order> query = new QueryWrapper<>();
        
        // 关键词搜索
        if (keyword != null && !keyword.isEmpty()) {
            query.and(wrapper -> wrapper
                    .like("order_id", keyword)
                    .or()
                    .like("user_name", keyword)
                    .or()
                    .like("pickup_code", keyword));
        }
        
        // 状态筛选
        if (status != null) {
            query.eq("order_status", status);
        }
        
        // 校区筛选
        if (campus != null && !campus.isEmpty()) {
            query.eq("campus", campus);
        }
        
        // 费用筛选
        if (minFee != null) {
            query.ge("fee", minFee);
        }
        
        // 日期筛选
        if (startDate != null && !startDate.isEmpty()) {
            query.ge("create_time", LocalDate.parse(startDate).atStartOfDay());
        }
        if (endDate != null && !endDate.isEmpty()) {
            query.le("create_time", LocalDate.parse(endDate).plusDays(1).atStartOfDay());
        }
        
        // 按创建时间降序排序
        query.orderByDesc("create_time");
        
        List<Order> orders = orderMapper.selectList(query);
        
        // 这里应该有导出Excel的逻辑，但为了简化，我们返回一个简单的CSV格式
        StringBuilder csv = new StringBuilder();
        csv.append("订单ID,用户名,快递点,取件码,送达地址,费用,代取员,状态,创建时间\n");
        
        for (Order order : orders) {
            csv.append(order.getOrderId()).append(",");
            csv.append(order.getUserName()).append(",");
            csv.append(order.getExpressPointName()).append(",");
            csv.append(order.getPickupCode()).append(",");
            csv.append(order.getDeliveryAddress()).append(",");
            csv.append(order.getFee()).append(",");
            csv.append(order.getCourierName() != null ? order.getCourierName() : "-").append(",");
            csv.append(getOrderStatusText(order.getOrderStatus())).append(",");
            csv.append(order.getCreateTime()).append("\n");
        }
        
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        headers.setContentDispositionFormData("attachment", "orders_" + LocalDate.now() + ".csv");
        
        return ResponseEntity
                .ok()
                .headers(headers)
                .body(csv.toString().getBytes());
    }
    
    private String getOrderStatusText(Integer status) {
        switch (status) {
            case 1: return "待接单";
            case 2: return "已接单";
            case 3: return "配送中";
            case 4: return "已完成";
            case 5: return "已取消";
            default: return "未知状态";
        }
    }
}