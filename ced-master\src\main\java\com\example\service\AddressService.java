package com.example.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.example.dto.AddressDTO;
import com.example.entity.Address;
import com.example.mapper.AddressMapper;
import com.example.util.R;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

@Service
public class AddressService {
    
    @Autowired
    private AddressMapper addressMapper;
    
    /**
     * 获取用户地址列表
     */
    public R<List<Address>> getUserAddresses(Long userId) {
        List<Address> addresses = addressMapper.findByUserId(userId);
        return R.ok(addresses);
    }
    
    /**
     * 添加地址
     */
    @Transactional
    public R<String> addAddress(Long userId, AddressDTO addressDTO) {
        Address address = new Address();
        BeanUtils.copyProperties(addressDTO, address);
        address.setUserId(userId);
        address.setCreateTime(LocalDateTime.now());
        address.setUpdateTime(LocalDateTime.now());
        
        // 如果设置为默认地址，先取消其他默认地址
        if (address.getIsDefault() == 1) {
            clearDefaultAddress(userId);
        }
        
        addressMapper.insert(address);
        return R.ok("地址添加成功");
    }
    
    /**
     * 更新地址
     */
    @Transactional
    public R<String> updateAddress(Long userId, Long addressId, AddressDTO addressDTO) {
        Address address = addressMapper.selectById(addressId);
        if (address == null || !address.getUserId().equals(userId)) {
            return R.fail("地址不存在");
        }
        
        BeanUtils.copyProperties(addressDTO, address);
        address.setUpdateTime(LocalDateTime.now());
        
        // 如果设置为默认地址，先取消其他默认地址
        if (address.getIsDefault() == 1) {
            clearDefaultAddress(userId);
        }
        
        addressMapper.updateById(address);
        return R.ok("地址更新成功");
    }
    
    /**
     * 删除地址
     */
    public R<String> deleteAddress(Long userId, Long addressId) {
        Address address = addressMapper.selectById(addressId);
        if (address == null || !address.getUserId().equals(userId)) {
            return R.fail("地址不存在");
        }
        
        addressMapper.deleteById(addressId);
        return R.ok("地址删除成功");
    }
    
    /**
     * 设置默认地址
     */
    @Transactional
    public R<String> setDefaultAddress(Long userId, Long addressId) {
        Address address = addressMapper.selectById(addressId);
        if (address == null || !address.getUserId().equals(userId)) {
            return R.fail("地址不存在");
        }
        
        // 先取消其他默认地址
        clearDefaultAddress(userId);
        
        // 设置当前地址为默认
        address.setIsDefault(1);
        address.setUpdateTime(LocalDateTime.now());
        addressMapper.updateById(address);
        
        return R.ok("默认地址设置成功");
    }
    
    /**
     * 清除用户的默认地址
     */
    private void clearDefaultAddress(Long userId) {
        LambdaQueryWrapper<Address> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Address::getUserId, userId)
                   .eq(Address::getIsDefault, 1);
        
        List<Address> defaultAddresses = addressMapper.selectList(queryWrapper);
        for (Address addr : defaultAddresses) {
            addr.setIsDefault(0);
            addr.setUpdateTime(LocalDateTime.now());
            addressMapper.updateById(addr);
        }
    }
}