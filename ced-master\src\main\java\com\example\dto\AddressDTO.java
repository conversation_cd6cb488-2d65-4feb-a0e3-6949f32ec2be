package com.example.dto;

import lombok.Data;
import javax.validation.constraints.NotBlank;

/**
 * 地址DTO
 */
@Data
public class AddressDTO {
    
    private Long addressId;
    
    @NotBlank(message = "校区不能为空")
    private String campus;
    
    @NotBlank(message = "楼栋不能为空")
    private String building;
    
    @NotBlank(message = "房间号不能为空")
    private String room;
    
    private String detailAddress;
    
    @NotBlank(message = "联系人姓名不能为空")
    private String contactName;
    
    @NotBlank(message = "联系电话不能为空")
    private String contactPhone;
    
    private Integer isDefault = 0;
}