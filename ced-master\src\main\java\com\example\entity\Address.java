package com.example.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 用户地址实体类 - 对应数据库address表
 */
@Data
@TableName("address")
public class Address {
    @TableId(type = IdType.AUTO)
    @TableField("address_id")
    private Long addressId;
    
    @TableField("user_id")
    private Long userId;
    
    private String campus; // 校区
    private String building; // 楼栋
    private String room; // 房间号
    
    @TableField("detail_address")
    private String detailAddress; // 详细地址
    
    @TableField("contact_name")
    private String contactName; // 联系人姓名
    
    @TableField("contact_phone")
    private String contactPhone; // 联系电话
    
    @TableField("is_default")
    private Integer isDefault; // 是否默认地址：1-是，0-否
    
    @TableField("create_time")
    private LocalDateTime createTime;
    
    @TableField("update_time")
    private LocalDateTime updateTime;
}