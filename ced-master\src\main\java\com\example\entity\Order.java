package com.example.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 订单实体类 - 对应数据库order_info表
 */
@Data
@TableName("order_info")
public class Order {
    @TableId(type = IdType.AUTO)
    @TableField("order_id")
    private Long orderId;
    
    @TableField("order_no")
    private String orderNo; // 订单号
    
    @TableField("user_id")
    private Long userId; // 下单用户ID
    
    @TableField("pickup_user_id")
    private Long pickupUserId; // 代取员ID
    
    @TableField("station_id")
    private Long stationId; // 快递站点ID
    
    @TableField("pickup_code")
    private String pickupCode; // 取件码
    
    @TableField("tracking_number")
    private String trackingNumber; // 运单号
    
    @TableField("express_description")
    private String expressDescription; // 快递描述
    
    @TableField("address_id")
    private Long addressId; // 收货地址ID
    
    @TableField("delivery_address")
    private String deliveryAddress; // 送达地址详情
    
    @TableField("contact_name")
    private String contactName; // 联系人
    
    @TableField("contact_phone")
    private String contactPhone; // 联系电话
    
    @TableField("expected_time")
    private LocalDateTime expectedTime; // 期望送达时间
    
    @TableField("pickup_time")
    private LocalDateTime pickupTime; // 取件时间
    
    @TableField("delivery_time")
    private LocalDateTime deliveryTime; // 送达时间
    
    @TableField("service_fee")
    private BigDecimal serviceFee; // 代取费用
    
    private BigDecimal deposit; // 定金
    
    @TableField("total_amount")
    private BigDecimal totalAmount; // 总金额
    
    @TableField("order_status")
    private Integer orderStatus; // 订单状态：1-待接单，2-已接单，3-取件中，4-配送中，5-待确认，6-已完成，7-已取消
    
    @TableField("pay_status")
    private Integer payStatus; // 支付状态：1-待支付定金，2-已支付定金，3-待支付尾款，4-已支付完成
    
    private String remark; // 备注
    
    @TableField("cancel_reason")
    private String cancelReason; // 取消原因
    
    @TableField("create_time")
    private LocalDateTime createTime;
    
    @TableField("update_time")
    private LocalDateTime updateTime;

    @TableField(exist = false)
    private Long courierId;
    @TableField(exist = false)
    private String courierName;
    @TableField(exist = false)
    private String note;
    @TableField(exist = false)
    private String userName;
    @TableField(exist = false)
    private String expressPointName;
    @TableField(exist = false)
    private BigDecimal fee;
    @TableField(exist = false)
    private Long expressPointId;
}