package com.example.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.entity.BaseEntity;
import com.example.entity.CourierComplaint;
import com.example.entity.SysUser;
import com.example.enums.Status;
import com.example.mapper.CourierComplaintMapper;
import com.example.mapper.UserMapper;
import com.example.util.R;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 管理员投诉管理控制器
 */
@RestController
@RequestMapping("/admin/complaints")
public class AdminComplaintController {
    
    @Autowired
    private CourierComplaintMapper complaintMapper;
    
    @Autowired
    private UserMapper userMapper;
    
    /**
     * 获取投诉列表
     */
    @GetMapping
    public R<Map<String, Object>> getComplaintList(
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) Integer complaintType,
            @RequestParam(required = false) Integer status,
            @RequestParam(required = false) Integer complaintLevel) {
        
        Page<CourierComplaint> page = new Page<>(current, size);
        LambdaQueryWrapper<CourierComplaint> query = new LambdaQueryWrapper<>();
        
        // 关键词搜索
        if (keyword != null && !keyword.isEmpty()) {
            query.like(CourierComplaint::getComplaintTitle, keyword)
                 .or()
                 .like(CourierComplaint::getComplaintContent, keyword);
        }
        
        // 投诉类型筛选
        if (complaintType != null) {
            query.eq(CourierComplaint::getComplaintType, complaintType);
        }
        
        // 状态筛选
        if (status != null) {
            query.eq(CourierComplaint::getStatus, status);
        }
        
        // 投诉级别筛选
        if (complaintLevel != null) {
            query.eq(CourierComplaint::getComplaintLevel, complaintLevel);
        }
        
        query.orderByDesc(CourierComplaint::getCreateTime);
        
        IPage<CourierComplaint> complaintPage = complaintMapper.selectPage(page, query);
        
        Map<String, Object> result = new HashMap<>();
        result.put("records", complaintPage.getRecords());
        result.put("total", complaintPage.getTotal());
        result.put("current", complaintPage.getCurrent());
        result.put("size", complaintPage.getSize());
        
        return R.ok(result);
    }
    
    /**
     * 获取投诉详情
     */
    @GetMapping("/{complaintId}")
    public R<CourierComplaint> getComplaintDetail(@PathVariable Long complaintId) {
        CourierComplaint complaint = complaintMapper.selectById(complaintId);
        if (complaint == null) {
            return R.fail("投诉记录不存在");
        }
        return R.ok(complaint);
    }
    
    /**
     * 处理投诉
     */
    @PostMapping("/{complaintId}/handle")
    public R<String> handleComplaint(
            @PathVariable Long complaintId,
            @RequestBody Map<String, Object> params) {
        
        CourierComplaint complaint = complaintMapper.selectById(complaintId);
        if (complaint == null) {
            return R.fail("投诉记录不存在");
        }
        
        String handleResult = (String) params.get("handleResult");
        Integer penaltyType = (Integer) params.get("penaltyType");
        Integer penaltyDuration = (Integer) params.get("penaltyDuration");
        String penaltyReason = (String) params.get("penaltyReason");
        Long handlerId = (Long) params.get("handlerId");
        
        // 更新投诉记录
        complaint.setStatus(3); // 已处理
        complaint.setHandleResult(handleResult);
        complaint.setPenaltyType(penaltyType);
        complaint.setPenaltyDuration(penaltyDuration);
        complaint.setPenaltyReason(penaltyReason);
        complaint.setHandlerId(handlerId);
        complaint.setHandleTime(LocalDateTime.now());
        complaint.setUpdateTime(LocalDateTime.now());
        
        complaintMapper.updateById(complaint);
        
        // 如果有处罚，更新代取员状态
        if (penaltyType != null && penaltyType > 0) {
            SysUser courier = userMapper.selectById(complaint.getCourierId());
            if (courier != null) {
                // 增加投诉次数
                Integer complaints = courier.getComplaints() != null ? courier.getComplaints() : 0;
                courier.setComplaints(complaints + 1);
                
                // 根据处罚类型更新状态
                switch (penaltyType) {
                    case 2: // 暂停服务
                        courier.setStatus(Status.pending);
                        break;
                    case 3: // 降级
                        Integer currentLevel = courier.getCourierLevel() != null ? courier.getCourierLevel() : 1;
                        if (currentLevel > 1) {
                            courier.setCourierLevel(currentLevel - 1);
                        }
                        break;
                    case 4: // 永久禁用
                        courier.setStatus(Status.disabled);
                        courier.setAuditStatus(2); // 审核拒绝
                        break;
                }
                
                courier.setUpdateTime(LocalDateTime.now());
                userMapper.updateById(courier);
            }
        }
        
        return R.ok("投诉处理完成");
    }
    
    /**
     * 关闭投诉
     */
    @PostMapping("/{complaintId}/close")
    public R<String> closeComplaint(@PathVariable Long complaintId) {
        CourierComplaint complaint = complaintMapper.selectById(complaintId);
        if (complaint == null) {
            return R.fail("投诉记录不存在");
        }
        
        complaint.setStatus(4); // 已关闭
        complaint.setUpdateTime(LocalDateTime.now());
        complaintMapper.updateById(complaint);
        
        return R.ok("投诉已关闭");
    }
    
    /**
     * 获取投诉统计数据
     */
    @GetMapping("/statistics")
    public R<Map<String, Object>> getComplaintStatistics() {
        Map<String, Object> statistics = new HashMap<>();
        
        // 总投诉数
        Integer totalComplaints = complaintMapper.selectCount(null);
        statistics.put("totalComplaints", totalComplaints);
        
        // 待处理投诉数
        LambdaQueryWrapper<CourierComplaint> pendingQuery = new LambdaQueryWrapper<>();
        pendingQuery.eq(CourierComplaint::getStatus, 1);
        Integer pendingComplaints = complaintMapper.selectCount(pendingQuery);
        statistics.put("pendingComplaints", pendingComplaints);
        
        // 已处理投诉数
        LambdaQueryWrapper<CourierComplaint> handledQuery = new LambdaQueryWrapper<>();
        handledQuery.eq(CourierComplaint::getStatus, 3);
        Integer handledComplaints = complaintMapper.selectCount(handledQuery);
        statistics.put("handledComplaints", handledComplaints);
        
        // 严重投诉数
        LambdaQueryWrapper<CourierComplaint> seriousQuery = new LambdaQueryWrapper<>();
        seriousQuery.eq(CourierComplaint::getComplaintLevel, 3);
        Integer seriousComplaints = complaintMapper.selectCount(seriousQuery);
        statistics.put("seriousComplaints", seriousComplaints);
        
        // 计算处理率
        double handleRate = totalComplaints > 0 ? (double) handledComplaints / totalComplaints * 100 : 0;
        statistics.put("handleRate", Math.round(handleRate * 100.0) / 100.0);
        
        return R.ok(statistics);
    }
}
