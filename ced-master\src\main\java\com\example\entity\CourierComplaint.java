package com.example.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 代取员投诉实体类
 */
@Data
@TableName("courier_complaint")
public class CourierComplaint {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    @TableField("order_id")
    private Long orderId; // 订单ID
    
    @TableField("courier_id")
    private Long courierId; // 被投诉代取员ID
    
    @TableField("complainant_id")
    private Long complainantId; // 投诉人ID
    
    @TableField("complaint_type")
    private Integer complaintType; // 投诉类型：1-服务态度，2-配送延迟，3-物品损坏，4-其他
    
    @TableField("complaint_title")
    private String complaintTitle; // 投诉标题
    
    @TableField("complaint_content")
    private String complaintContent; // 投诉内容
    
    @TableField("evidence_urls")
    private String evidenceUrls; // 证据图片URLs（JSON格式）
    
    @TableField("complaint_level")
    private Integer complaintLevel; // 投诉级别：1-轻微，2-一般，3-严重
    
    private Integer status; // 处理状态：1-待处理，2-处理中，3-已处理，4-已关闭
    
    @TableField("handler_id")
    private Long handlerId; // 处理人ID
    
    @TableField("handle_result")
    private String handleResult; // 处理结果
    
    @TableField("handle_time")
    private LocalDateTime handleTime; // 处理时间
    
    @TableField("penalty_type")
    private Integer penaltyType; // 处罚类型：0-无处罚，1-警告，2-暂停服务，3-降级，4-永久禁用
    
    @TableField("penalty_duration")
    private Integer penaltyDuration; // 处罚时长（天）
    
    @TableField("penalty_reason")
    private String penaltyReason; // 处罚原因
    
    @TableField("create_time")
    private LocalDateTime createTime;
    
    @TableField("update_time")
    private LocalDateTime updateTime;
}
