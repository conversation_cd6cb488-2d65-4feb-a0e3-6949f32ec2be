package com.example.dto;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.TableField;
import com.example.constant.Constants;
import com.example.enums.Role;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class RegisterDTO {

    @NotBlank(message = "用户名不能为空")
    @Length(min = Constants.USERNAME_MIN_LENGTH, max = Constants.USERNAME_MAX_LENGTH, message = "用户名限制2-20个字符")
    private String username;

    @NotBlank(message = "用户密码不能为空")
    @Length(min = Constants.PASSWORD_MIN_LENGTH, max = Constants.PASSWORD_MAX_LENGTH, message = "用户密码限制5-20个字符")
    private String password;

    @NotBlank(message = "真实姓名不能为空")
    private String realName;

    @NotBlank(message = "手机号不能为空")
    private String phone;


    private String idCard;
    private String email;
    private String alipayAccount;

    private String wechatAccount;

    @NotNull(message = "用户类型不能为空")
    @EnumValue
    private Role role;
}
