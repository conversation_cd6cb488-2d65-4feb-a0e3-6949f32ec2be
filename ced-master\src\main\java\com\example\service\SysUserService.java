package com.example.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.example.config.ExpressConfig;
import com.example.constant.Constants;
import com.example.dto.RegisterDTO;
import com.example.dto.UserUpdateDTO;
import com.example.dto.PasswordUpdateDTO;
import com.example.entity.BaseEntity;
import com.example.entity.SysUser;
import com.example.entity.Order;
import com.example.entity.PickupSettings;
import com.example.enums.Status;
import com.example.mapper.UserMapper;
import com.example.mapper.OrderMapper;
import com.example.mapper.PickupSettingsMapper;
import com.example.dto.PickupSettingsDTO;
import com.example.util.*;
import com.example.vo.UserProfileVO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class SysUserService {
    @Resource
    private UserMapper userMapper;
    @Resource
    private OrderMapper orderMapper;
    @Resource
    private PickupSettingsMapper pickupSettingsMapper;
    @Resource
    private RedisCache redisCache;

    public SysUser checkUser(String username, String password) {
        LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysUser::getUsername, username);
        SysUser user = userMapper.selectOne(queryWrapper);
        
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        if (Constants.ACCOUNT_ACTIVE.equals(user.getStatus())) {
            throw new RuntimeException("用户已被禁用");
        }
        
        String encryptedPassword = MD5Utils.code(password);
        if (encryptedPassword != null && !encryptedPassword.equals(user.getPassword())) {
            throw new RuntimeException("密码错误");
        }

        return user;
    }

    public void updateLoginInfo(SysUser user) {
        user.setUpdateTime(LocalDateTime.now());
        userMapper.updateById(user);
    }

    public void registerUser(RegisterDTO registerDTO) {
        // 检查用户名是否已存在
        LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysUser::getUsername, registerDTO.getUsername());
        if (userMapper.selectOne(queryWrapper) != null) {
            throw new RuntimeException("用户名已存在");
        }

        // 检查手机号是否已存在
        queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysUser::getPhone, registerDTO.getPhone());
        if (userMapper.selectOne(queryWrapper) != null) {
            throw new RuntimeException("手机号已被注册");
        }

        
        // 创建新用户
        SysUser user = new SysUser();
        user.setUsername(registerDTO.getUsername());
        user.setPassword(MD5Utils.code(registerDTO.getPassword()));
        user.setPhone(registerDTO.getPhone());
        user.setRealName(registerDTO.getRealName());
        user.setIdCard(registerDTO.getIdCard());
        user.setEmail(registerDTO.getEmail());
        user.setWechatAccount(registerDTO.getWechatAccount());
        user.setAlipayAccount(registerDTO.getAlipayAccount());
//        user.setRole(registerDTO.getRole());
        user.setStatus(Status.active);
        user.setCreateTime(LocalDateTime.now());
        user.setUpdateTime(LocalDateTime.now());
        userMapper.insert(user);
    }

    public void validateCaptcha(String code, String uuid) {
        String verifyKey = Constants.CAPTCHA_CODE_KEY + StringUtils.nvl(uuid, "");
        String captcha = redisCache.getCacheObject(verifyKey);
        redisCache.deleteObject(verifyKey);
        if (captcha == null)
        {
            throw new RuntimeException("验证码错误");
        }
        if (!code.equalsIgnoreCase(captcha))
        {
            throw new RuntimeException("验证码错误");
        }
    }
    
    public SysUser getUserById(Long userId) {
        return userMapper.selectById(userId);
    }
    
    public List<SysUser> getPendingPickupUsers() {
        LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysUser::getUserType, 2)
                   .eq(SysUser::getAuditStatus, 0);
        return userMapper.selectList(queryWrapper);
    }
    
    public void auditPickupUser(Long userId, Integer auditStatus) {
        SysUser user = userMapper.selectById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }
        
        /*if (user.getUserType() != 2) {
            throw new RuntimeException("该用户不是代取员");
        }*/
        
        user.setAuditStatus(auditStatus);
        user.setUpdateTime(LocalDateTime.now());
        userMapper.updateById(user);
    }

    /**
     * 获取用户详细资料
     */
    public UserProfileVO getUserProfile(Long userId) {
        SysUser user = userMapper.selectById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        UserProfileVO profile = new UserProfileVO();
        BeanUtils.copyProperties(user, profile);
        
        // 设置用户类型名称
//        profile.setUserTypeName(getUserTypeName(user.getUserType()));
        profile.setAuditStatusName(getAuditStatusName(user.getAuditStatus()));
        
        // 获取统计信息
        Map<String, Object> statistics = getUserStatistics(userId);
        profile.setTotalOrders((Integer) statistics.get("totalOrders"));
        profile.setCompletedOrders((Integer) statistics.get("completedOrders"));
        profile.setCancelledOrders((Integer) statistics.get("cancelledOrders"));
        profile.setTotalEarnings((BigDecimal) statistics.get("totalEarnings"));
        profile.setTotalSpent((BigDecimal) statistics.get("totalSpent"));
        
        return profile;
    }

    /**
     * 更新用户基本信息
     */
    public void updateUserProfile(Long userId, UserUpdateDTO userUpdateDTO) {
        SysUser user = userMapper.selectById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        // 检查手机号是否被其他用户使用
        if (!user.getPhone().equals(userUpdateDTO.getPhone())) {
            LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(SysUser::getPhone, userUpdateDTO.getPhone())
                       .ne(SysUser::getUserId, userId);
            if (userMapper.selectOne(queryWrapper) != null) {
                throw new RuntimeException("手机号已被其他用户使用");
            }
        }

        // 更新用户信息
        user.setRealName(userUpdateDTO.getRealName());
        user.setPhone(userUpdateDTO.getPhone());
        user.setAlipayAccount(userUpdateDTO.getAlipayAccount());
        user.setWechatAccount(userUpdateDTO.getWechatAccount());
        user.setEmail(userUpdateDTO.getEmail());
        if (userUpdateDTO.getAvatar() != null) {
            user.setAvatar(userUpdateDTO.getAvatar());
        }
        user.setUpdateTime(LocalDateTime.now());
        
        userMapper.updateById(user);
    }

    /**
     * 修改密码
     */
    public void updatePassword(Long userId, PasswordUpdateDTO passwordUpdateDTO) {
        if (!passwordUpdateDTO.getNewPassword().equals(passwordUpdateDTO.getConfirmPassword())) {
            throw new RuntimeException("两次输入的新密码不一致");
        }

        SysUser user = userMapper.selectById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        // 验证原密码
        String oldPasswordEncrypted = MD5Utils.code(passwordUpdateDTO.getOldPassword());
        if (!oldPasswordEncrypted.equals(user.getPassword())) {
            throw new RuntimeException("原密码错误");
        }

        // 更新密码
        user.setPassword(MD5Utils.code(passwordUpdateDTO.getNewPassword()));
        user.setUpdateTime(LocalDateTime.now());
        userMapper.updateById(user);
    }

    /**
     * 上传头像
     */
    public String uploadAvatar(Long userId, MultipartFile file) {
        try {
            // 验证文件类型
            String contentType = file.getContentType();
            if (contentType == null || (!contentType.equals("image/jpeg") && !contentType.equals("image/png"))) {
                throw new RuntimeException("只支持JPG和PNG格式的图片");
            }

            // 验证文件大小（2MB）
            if (file.getSize() > 2 * 1024 * 1024) {
                throw new RuntimeException("文件大小不能超过2MB");
            }

            // 保存文件
            String fileName = "/avatar_" + userId + "_" + System.currentTimeMillis() + getFileExtension(file.getOriginalFilename());
            uploadFile(file, fileName); // 保存文件到磁盘

            // 更新用户头像路径（保存相对路径）
            SysUser user = userMapper.selectById(userId);
            if (user != null) {
                user.setAvatar(fileName); // 保存相对路径而不是完整路径
                user.setUpdateTime(LocalDateTime.now());
                userMapper.updateById(user);
            }

            return fileName; // 返回相对路径
        } catch (IOException e) {
            throw new RuntimeException("头像上传失败：" + e.getMessage());
        }
    }

    public String uploadFile(MultipartFile file, String path) throws IOException {
        String filePath = ExpressConfig.getProfile()+ path;
        File desc = new File(filePath);
        if (!desc.exists())
        {
            if (!desc.getParentFile().exists())
            {
                desc.getParentFile().mkdirs();
            }
        }
        String absPath = desc.getAbsolutePath();
        file.transferTo(Paths.get(absPath));
        return filePath;
    }

    /**
     * 删除头像
     */
    public void deleteAvatar(Long userId) {
        SysUser user = userMapper.selectById(userId);
        if (user != null && user.getAvatar() != null) {
            // 删除文件
            FileUtils.deleteFile(user.getAvatar());
            
            // 更新数据库
            user.setAvatar(null);
            user.setUpdateTime(LocalDateTime.now());
            userMapper.updateById(user);
        }
    }

    /**
     * 获取用户统计信息
     */
    public Map<String, Object> getUserStatistics(Long userId) {
        Map<String, Object> statistics = new HashMap<>();
        
        // 查询订单统计
        LambdaQueryWrapper<Order> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Order::getUserId, userId);
        List<Order> userOrders = orderMapper.selectList(queryWrapper);
        
        int totalOrders = userOrders.size();
        int completedOrders = (int) userOrders.stream().filter(order -> order.getOrderStatus() == 6).count();
        int cancelledOrders = (int) userOrders.stream().filter(order -> order.getOrderStatus() == 7).count();
        
        BigDecimal totalSpent = userOrders.stream()
                .filter(order -> order.getOrderStatus() == 6)
                .map(Order::getTotalAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        statistics.put("totalOrders", totalOrders);
        statistics.put("completedOrders", completedOrders);
        statistics.put("cancelledOrders", cancelledOrders);
        statistics.put("totalSpent", totalSpent);

        // 如果是代取员，计算收益
        SysUser user = userMapper.selectById(userId);
        /*if (user != null && user.getUserType() == 2) {
            LambdaQueryWrapper<Order> pickupQueryWrapper = new LambdaQueryWrapper<>();
            pickupQueryWrapper.eq(Order::getPickupUserId, userId);
            List<Order> pickupOrders = orderMapper.selectList(pickupQueryWrapper);
            
            BigDecimal totalEarnings = pickupOrders.stream()
                    .filter(order -> order.getOrderStatus() == 6)
                    .map(Order::getServiceFee)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            
            statistics.put("totalEarnings", totalEarnings);
            statistics.put("pickupOrders", pickupOrders.size());
        } else {
            statistics.put("totalEarnings", BigDecimal.ZERO);
            statistics.put("pickupOrders", 0);
        }*/

        return statistics;
    }

    /**
    /**
     * 获取代取员接单设置
     */
    public Map<String, Object> getPickupSettings(Long userId) {
        LambdaQueryWrapper<PickupSettings> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PickupSettings::getUserId, userId);
        PickupSettings settings = pickupSettingsMapper.selectOne(queryWrapper);
        
        Map<String, Object> result = new HashMap<>();
        if (settings != null) {
            result.put("acceptOrders", settings.getAcceptOrders());
            result.put("workTimeStart", settings.getWorkTimeStart());
            result.put("workTimeEnd", settings.getWorkTimeEnd());
            result.put("campusRange", settings.getCampusRange());
            result.put("minFee", settings.getMinFee());
            result.put("remark", settings.getRemark());
        } else {
            // 返回默认设置
            result.put("acceptOrders", true);
            result.put("workTimeStart", "08:00");
            result.put("workTimeEnd", "22:00");
            result.put("campusRange", "[\"main\", \"south\"]");
            result.put("minFee", new BigDecimal("3.00"));
            result.put("remark", "");
        }
        return result;
    }

    /**
     * 更新代取员接单设置
     */
    public void updatePickupSettings(Long userId, Object pickupSettingsObj) {
        // 将Object转换为Map
        @SuppressWarnings("unchecked")
        Map<String, Object> settingsMap = (Map<String, Object>) pickupSettingsObj;
        
        LambdaQueryWrapper<PickupSettings> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PickupSettings::getUserId, userId);
        PickupSettings existingSettings = pickupSettingsMapper.selectOne(queryWrapper);
        
        if (existingSettings != null) {
            // 更新现有设置
            existingSettings.setAcceptOrders((Boolean) settingsMap.get("acceptOrders"));
            existingSettings.setWorkTimeStart((String) settingsMap.get("workTimeStart"));
            existingSettings.setWorkTimeEnd((String) settingsMap.get("workTimeEnd"));
            existingSettings.setCampusRange(settingsMap.get("campusRange").toString());
            existingSettings.setMinFee(new BigDecimal(settingsMap.get("minFee").toString()));
            existingSettings.setRemark((String) settingsMap.get("remark"));
            existingSettings.setUpdateTime(LocalDateTime.now());
            pickupSettingsMapper.updateById(existingSettings);
        } else {
            // 创建新设置
            PickupSettings newSettings = new PickupSettings();
            newSettings.setUserId(userId);
            newSettings.setAcceptOrders((Boolean) settingsMap.get("acceptOrders"));
            newSettings.setWorkTimeStart((String) settingsMap.get("workTimeStart"));
            newSettings.setWorkTimeEnd((String) settingsMap.get("workTimeEnd"));
            newSettings.setCampusRange(settingsMap.get("campusRange").toString());
            newSettings.setMinFee(new BigDecimal(settingsMap.get("minFee").toString()));
            newSettings.setRemark((String) settingsMap.get("remark"));
            newSettings.setCreateTime(LocalDateTime.now());
            newSettings.setUpdateTime(LocalDateTime.now());
            pickupSettingsMapper.insert(newSettings);
        }
    }

    private String getUserTypeName(Integer userType) {
        switch (userType) {
            case 1: return "普通用户";
            case 2: return "代取员";
            case 3: return "管理员";
            default: return "未知";
        }
    }

    private String getAuditStatusName(Integer auditStatus) {
        switch (auditStatus) {
            case 0: return "待审核";
            case 1: return "审核通过";
            case 2: return "审核拒绝";
            default: return "未知";
        }
    }

    private String getFileExtension(String fileName) {
        if (fileName == null || fileName.lastIndexOf(".") == -1) {
            return "";
        }
        return fileName.substring(fileName.lastIndexOf("."));
    }
}
