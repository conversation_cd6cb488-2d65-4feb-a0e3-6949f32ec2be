-- 创建操作日志表
CREATE TABLE IF NOT EXISTS `operation_log` (
  `log_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `operator_id` bigint(20) DEFAULT NULL COMMENT '操作员ID',
  `operator_name` varchar(50) DEFAULT NULL COMMENT '操作员姓名',
  `operator_type` int(11) DEFAULT NULL COMMENT '操作员类型：1-管理员，2-代取员，3-学生',
  `action_type` varchar(50) DEFAULT NULL COMMENT '操作类型',
  `action_name` varchar(100) DEFAULT NULL COMMENT '具体操作名称',
  `target_type` varchar(50) DEFAULT NULL COMMENT '操作对象类型',
  `target_id` bigint(20) DEFAULT NULL COMMENT '操作对象ID',
  `target_name` varchar(100) DEFAULT NULL COMMENT '操作对象名称',
  `description` text COMMENT '操作描述',
  `request_method` varchar(10) DEFAULT NULL COMMENT '请求方法',
  `request_url` varchar(500) DEFAULT NULL COMMENT '请求URL',
  `request_params` text COMMENT '请求参数',
  `response_result` text COMMENT '响应结果',
  `ip_address` varchar(50) DEFAULT NULL COMMENT '操作IP地址',
  `user_agent` text COMMENT '用户代理',
  `status` int(11) DEFAULT 1 COMMENT '操作状态：1-成功，0-失败',
  `error_message` text COMMENT '错误信息',
  `execution_time` bigint(20) DEFAULT NULL COMMENT '执行时间（毫秒）',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`log_id`),
  KEY `idx_operator_id` (`operator_id`),
  KEY `idx_action_type` (`action_type`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='操作日志表';

-- 插入一些示例数据
INSERT INTO `operation_log` (`operator_id`, `operator_name`, `operator_type`, `action_type`, `action_name`, `target_type`, `target_id`, `target_name`, `description`, `request_method`, `request_url`, `ip_address`, `status`, `execution_time`, `create_time`) VALUES
(1, '系统管理员', 1, '用户管理', '创建用户', '用户', 2, '张三', '创建新用户：张三', 'POST', '/admin/users', '*************', 1, 150, NOW() - INTERVAL 1 DAY),
(1, '系统管理员', 1, '用户管理', '编辑用户', '用户', 2, '张三', '编辑用户信息：张三', 'PUT', '/admin/users/2', '*************', 1, 120, NOW() - INTERVAL 2 HOUR),
(1, '系统管理员', 1, '系统配置', '发布公告', '公告', 1, '系统维护通知', '发布系统公告：系统维护通知', 'POST', '/admin/notices', '*************', 1, 200, NOW() - INTERVAL 3 HOUR),
(1, '系统管理员', 1, '用户管理', '审核代取员', '用户', 3, '李四', '审核代取员申请：李四', 'POST', '/admin/pickup-users/3/audit', '*************', 1, 180, NOW() - INTERVAL 4 HOUR),
(1, '系统管理员', 1, '订单管理', '分配代取员', '订单', 1001, 'ORD20240101001', '为订单分配代取员', 'POST', '/admin/orders/1001/assign', '*************', 1, 100, NOW() - INTERVAL 5 HOUR),
(1, '系统管理员', 1, '数据导出', '导出用户数据', '用户', NULL, '用户列表', '导出用户数据到Excel', 'GET', '/admin/users/export', '*************', 1, 300, NOW() - INTERVAL 6 HOUR),
(1, '系统管理员', 1, '系统配置', '添加快递站点', '快递站点', 1, '菜鸟驿站', '添加新的快递站点', 'POST', '/admin/stations', '*************', 1, 90, NOW() - INTERVAL 7 HOUR),
(1, '系统管理员', 1, '用户管理', '重置密码', '用户', 4, '王五', '重置用户密码：王五', 'POST', '/admin/users/4/reset-password', '*************', 1, 80, NOW() - INTERVAL 8 HOUR),
(1, '系统管理员', 1, '用户管理', '禁用用户', '用户', 5, '赵六', '禁用用户账户：赵六', 'POST', '/admin/users/5/status', '*************', 1, 70, NOW() - INTERVAL 9 HOUR),
(1, '系统管理员', 1, '系统配置', '撤回公告', '公告', 2, '节假日通知', '撤回系统公告：节假日通知', 'POST', '/admin/notices/2/unpublish', '*************', 1, 110, NOW() - INTERVAL 10 HOUR);
