package com.example.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.annotation.OperationLog;
import com.example.dto.UserUpdateRequest;
import com.example.dto.UserCreateRequest;
import com.example.entity.ExpressStation;
import com.example.entity.SysUser;
import com.example.mapper.ExpressStationMapper;
import com.example.mapper.OrderMapper;
import com.example.mapper.UserMapper;
import com.example.service.OperationLogService;
import com.example.util.R;
import com.example.vo.OperationLogVO;
import com.example.vo.UserVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 管理员控制器
 */
@Slf4j
@RestController
@RequestMapping("/admin")
public class AdminController {
    
    @Autowired
    private UserMapper userMapper;

    @Autowired
    private OrderMapper orderMapper;

    @Autowired
    private ExpressStationMapper expressStationMapper;

    @Autowired
    private OperationLogService operationLogService;
    
    /**
     * 获取系统统计数据
     */
    /*@GetMapping("/statistics")
    public R<Map<String, Object>> getStatistics() {
        Map<String, Object> stats = new HashMap<>();
        
        // 用户统计
        QueryWrapper<SysUser> userQuery = new QueryWrapper<>();
        userQuery.eq("user_type", 1);
        int normalUserCount = userMapper.selectCount(userQuery);
        
        userQuery = new QueryWrapper<>();
        userQuery.eq("user_type", 2);
        int pickupUserCount = userMapper.selectCount(userQuery);
        
        // 订单统计
        int totalOrders = orderMapper.selectCount(null);
        
        QueryWrapper orderQuery = new QueryWrapper<>();
        orderQuery.eq("order_status", 6);
        int completedOrders = orderMapper.selectCount(orderQuery);
        
        // 快递站点统计
        int stationCount = expressStationMapper.selectCount(null);
        
        stats.put("normalUserCount", normalUserCount);
        stats.put("pickupUserCount", pickupUserCount);
        stats.put("totalOrders", totalOrders);
        stats.put("completedOrders", completedOrders);
        stats.put("stationCount", stationCount);
        
        return R.ok(stats);
    }*/
    
    /**
     * 查询待审核的代取员列表
     */
    @GetMapping("/pickup-users/pending")
    public R<List<UserVO>> getPendingPickupUsers() {
        QueryWrapper<SysUser> query = new QueryWrapper<>();
        query.eq("user_type", 2).eq("audit_status", 0);
        List<SysUser> users = userMapper.selectList(query);
        
        List<UserVO> userVOs = new ArrayList<>();
        for (SysUser user : users) {
            UserVO userVO = new UserVO();
            BeanUtils.copyProperties(user, userVO);
            userVO.setUserTypeText(user.getUserType().getValue());
            userVO.setAuditStatusText(getAuditStatusText(user.getAuditStatus()));
            userVOs.add(userVO);
        }
        
        return R.ok(userVOs);
    }
    
    /**
     * 审核代取员
     */
    @PostMapping("/pickup-users/{userId}/audit")
    public R<String> auditPickupUser(@PathVariable Long userId, @RequestParam Integer auditStatus) {
        if (auditStatus != 1 && auditStatus != 2) {
            return R.fail("审核状态参数错误");
        }

        SysUser user = userMapper.selectById(userId);
        /*if (user == null || user.getUserType() != 2) {
            return R.fail("用户不存在或不是代取员");
        }*/
        
        user.setAuditStatus(auditStatus);
        user.setUpdateTime(LocalDateTime.now());
        userMapper.updateById(user);
        
        String result = auditStatus == 1 ? "审核通过" : "审核拒绝";
        return R.ok(result);
    }
    
    /**
     * 查询所有用户列表（分页）
     */
    @GetMapping("/users")
    public R<Map<String, Object>> getAllUsers(
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) Integer userType,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) String campus,
            @RequestParam(required = false) Integer status,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {

        Page<SysUser> page = new Page<>(current, size);
        QueryWrapper<SysUser> query = new QueryWrapper<>();

        // 用户类型筛选
        if (userType != null) {
            query.eq("user_type", userType);
        }

        // 关键词搜索
        if (keyword != null && !keyword.isEmpty()) {
            query.and(wrapper -> wrapper
                    .like("real_name", keyword)
                    .or()
                    .like("username", keyword)
                    .or()
                    .like("phone", keyword)
                    .or()
                    .like("student_id", keyword));
        }

        // 校区筛选
        if (campus != null && !campus.isEmpty()) {
            query.like("campus", campus);
        }

        // 状态筛选
        if (status != null) {
            query.eq("status", status);
        }

        // 日期筛选
        if (startDate != null && !startDate.isEmpty()) {
            query.ge("create_time", LocalDate.parse(startDate).atStartOfDay());
        }
        if (endDate != null && !endDate.isEmpty()) {
            query.le("create_time", LocalDate.parse(endDate).plusDays(1).atStartOfDay());
        }

        query.orderByDesc("create_time");

        IPage<SysUser> userPage = userMapper.selectPage(page, query);
        List<UserVO> userVOs = userPage.getRecords().stream().map(user -> {
            UserVO userVO = new UserVO();
            BeanUtils.copyProperties(user, userVO);
            userVO.setUserTypeText(user.getUserType().getValue());
            userVO.setAuditStatusText(getAuditStatusText(user.getAuditStatus()));
            return userVO;
        }).collect(Collectors.toList());

        Map<String, Object> result = new HashMap<>();
        result.put("records", userVOs);
        result.put("total", userPage.getTotal());
        result.put("current", userPage.getCurrent());
        result.put("size", userPage.getSize());

        return R.ok(result);
    }
    
    /**
     * 禁用/启用用户
     */
    @PostMapping("/users/{userId}/status")
    public R<String> updateUserStatus(@PathVariable Long userId, @RequestParam Integer status) {
        if (status != 0 && status != 1) {
            return R.fail("状态参数错误");
        }

        SysUser user = userMapper.selectById(userId);
        if (user == null) {
            return R.fail("用户不存在");
        }

        user.setUpdateTime(LocalDateTime.now());
        userMapper.updateById(user);

        String result = status == 1 ? "启用成功" : "禁用成功";
        return R.ok(result);
    }

    /**
     * 获取用户详情
     */
    @GetMapping("/users/{userId}/detail")
    public R<UserVO> getUserDetail(@PathVariable Long userId) {
        try {
            SysUser user = userMapper.selectById(userId);
            if (user == null) {
                return R.fail("用户不存在");
            }

            UserVO userVO = new UserVO();
            userVO.setUserId(user.getUserId());
            userVO.setUsername(user.getUsername());
            userVO.setRealName(user.getRealName());
            userVO.setPhone(user.getPhone());
            userVO.setEmail(user.getEmail());
//            userVO.setUserType(user.getUserType().getValue());
            userVO.setAvatar(user.getAvatar());
            userVO.setCreateTime(user.getCreateTime());

            // 根据用户类型设置特定信息


            userVO.setUserTypeText(user.getUserType().getValue());

            return R.ok(userVO);
        } catch (Exception e) {
            log.error("获取用户详情失败", e);
            return R.fail("获取用户详情失败：" + e.getMessage());
        }
    }

    /**
     * 重置用户密码
     */
    @PostMapping("/users/{userId}/reset-password")
    public R<String> resetUserPassword(@PathVariable Long userId) {
        try {
            SysUser user = userMapper.selectById(userId);
            if (user == null) {
                return R.fail("用户不存在");
            }

            // 重置密码为默认密码 123456
            String defaultPassword = "123456";

            user.setPassword(defaultPassword);
            user.setUpdateTime(LocalDateTime.now());

            int result = userMapper.updateById(user);

            if (result > 0) {
                return R.ok("密码重置成功，新密码为：" + defaultPassword);
            } else {
                return R.fail("密码重置失败");
            }
        } catch (Exception e) {
            log.error("重置用户密码失败", e);
            return R.fail("重置用户密码失败：" + e.getMessage());
        }
    }

    /**
     * 更新用户信息
     */
    @OperationLog(
        actionType = "用户管理",
        actionName = "编辑用户",
        targetType = "用户",
        description = "编辑用户信息"
    )
    @PutMapping("/users/{userId}")
    public R<String> updateUserInfo(@PathVariable Long userId, @RequestBody UserUpdateRequest request) {
        try {
            SysUser user = userMapper.selectById(userId);
            if (user == null) {
                return R.fail("用户不存在");
            }

            // 更新用户信息
            if (request.getRealName() != null) {
                user.setRealName(request.getRealName());
            }
            if (request.getPhone() != null) {
                user.setPhone(request.getPhone());
            }
            if (request.getEmail() != null) {
                user.setEmail(request.getEmail());
            }

            if (request.getCampus() != null) {
                user.setCampus(request.getCampus());
            }
            if (request.getDormBuilding() != null) {
                user.setDormBuilding(request.getDormBuilding());
            }
            if (request.getRoomNumber() != null) {
                user.setRoomNumber(request.getRoomNumber());
            }
            if (request.getAlipayAccount() != null) {
                user.setAlipayAccount(request.getAlipayAccount());
            }
            if (request.getWechatAccount() != null) {
                user.setWechatAccount(request.getWechatAccount());
            }


            user.setUpdateTime(LocalDateTime.now());

            int result = userMapper.updateById(user);

            if (result > 0) {
                return R.ok("用户信息更新成功");
            } else {
                return R.fail("用户信息更新失败");
            }
        } catch (Exception e) {
            log.error("更新用户信息失败", e);
            return R.fail("更新用户信息失败：" + e.getMessage());
        }
    }

    /**
     * 创建新用户
     */
    @OperationLog(
        actionType = "用户管理",
        actionName = "创建用户",
        targetType = "用户",
        description = "创建新用户账户"
    )
    @PostMapping("/users")
    public R<String> createUser(@RequestBody UserCreateRequest request) {
        try {
            // 检查用户名是否已存在
            QueryWrapper<SysUser> usernameQuery = new QueryWrapper<>();
            usernameQuery.eq("username", request.getUsername());
            SysUser existingUser = userMapper.selectOne(usernameQuery);
            if (existingUser != null) {
                return R.fail("用户名已存在");
            }

            // 检查手机号是否已存在
            QueryWrapper<SysUser> phoneQuery = new QueryWrapper<>();
            phoneQuery.eq("phone", request.getPhone());
            SysUser existingPhone = userMapper.selectOne(phoneQuery);
            if (existingPhone != null) {
                return R.fail("手机号已存在");
            }

            // 如果是学生，检查学号是否已存在
            if (request.getUserType() == 1 && request.getStudentId() != null) {
                QueryWrapper<SysUser> studentQuery = new QueryWrapper<>();
                studentQuery.eq("student_id", request.getStudentId());
                SysUser existingStudent = userMapper.selectOne(studentQuery);
                if (existingStudent != null) {
                    return R.fail("学号已存在");
                }
            }

            // 创建新用户
            SysUser newUser = new SysUser();
            newUser.setUsername(request.getUsername());
            newUser.setPassword(request.getPassword()); // 实际项目中应该加密密码
            newUser.setRealName(request.getRealName());
            newUser.setPhone(request.getPhone());
            newUser.setEmail(request.getEmail());
            newUser.setCampus(request.getCampus());
            newUser.setDormBuilding(request.getDormBuilding());
            newUser.setRoomNumber(request.getRoomNumber());
            newUser.setAlipayAccount(request.getAlipayAccount());
            newUser.setWechatAccount(request.getWechatAccount());
//            newUser.setUserType(request.getUserType());
            newUser.setAuditStatus(request.getUserType() == 2 ? 0 : 1); // 代取员需要审核，其他用户直接通过
            newUser.setCreateTime(LocalDateTime.now());
            newUser.setUpdateTime(LocalDateTime.now());

            int result = userMapper.insert(newUser);

            if (result > 0) {
                return R.ok("用户创建成功");
            } else {
                return R.fail("用户创建失败");
            }
        } catch (Exception e) {
            log.error("创建用户失败", e);
            return R.fail("创建用户失败：" + e.getMessage());
        }
    }
    
    /**
     * 添加快递站点
     */
    @PostMapping("/stations")
    public R<String> addStation(@RequestBody ExpressStation station) {
        station.setCreateTime(LocalDateTime.now());
        station.setUpdateTime(LocalDateTime.now());
        station.setStatus(1);
        
        expressStationMapper.insert(station);
        return R.ok("快递站点添加成功");
    }
    
    /**
     * 更新快递站点
     */
    @PutMapping("/stations/{stationId}")
    public R<String> updateStation(@PathVariable Long stationId, @RequestBody ExpressStation station) {
        ExpressStation existingStation = expressStationMapper.selectById(stationId);
        if (existingStation == null) {
            return R.fail("快递站点不存在");
        }
        
        station.setStationId(stationId);
        station.setUpdateTime(LocalDateTime.now());
        expressStationMapper.updateById(station);
        
        return R.ok("快递站点更新成功");
    }
    
    /**
     * 删除快递站点
     */
    @DeleteMapping("/stations/{stationId}")
    public R<String> deleteStation(@PathVariable Long stationId) {
        ExpressStation station = expressStationMapper.selectById(stationId);
        if (station == null) {
            return R.fail("快递站点不存在");
        }
        
        expressStationMapper.deleteById(stationId);
        return R.ok("快递站点删除成功");
    }
    
    private String getUserTypeText(Integer userType) {
        switch (userType) {
            case 1: return "普通用户";
            case 2: return "代取员";
            case 3: return "管理员";
            default: return "未知";
        }
    }
    
    private String getStatusText(Integer status) {
        return status == 1 ? "正常" : "禁用";
    }
    
    private String getAuditStatusText(Integer auditStatus) {
        switch (auditStatus) {
            case 0: return "待审核";
            case 1: return "审核通过";
            case 2: return "审核拒绝";
            default: return "未知";
        }
    }

    // ===== 操作日志接口 =====

    /**
     * 获取操作日志列表（分页）
     */
    @GetMapping("/operation-logs")
    public R<Map<String, Object>> getOperationLogs(
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String actionType,
            @RequestParam(required = false) String operatorName,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") java.time.LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") java.time.LocalDate endDate) {

        try {
            java.time.LocalDateTime startTime = startDate != null ? startDate.atStartOfDay() : null;
            java.time.LocalDateTime endTime = endDate != null ? endDate.plusDays(1).atStartOfDay() : null;

            IPage<OperationLogVO> logPage = operationLogService.getOperationLogs(
                    current, size, actionType, operatorName, startTime, endTime);

            Map<String, Object> result = new HashMap<>();
            result.put("records", logPage.getRecords());
            result.put("total", logPage.getTotal());
            result.put("current", logPage.getCurrent());
            result.put("size", logPage.getSize());

            return R.ok(result);
        } catch (Exception e) {
            log.error("获取操作日志失败", e);
            return R.fail("获取操作日志失败");
        }
    }

    /**
     * 导出操作日志
     */
    @GetMapping("/operation-logs/export")
    public ResponseEntity<byte[]> exportOperationLogs(
            @RequestParam(required = false) String actionType,
            @RequestParam(required = false) String operatorName,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") java.time.LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") java.time.LocalDate endDate) {

        try {
            java.time.LocalDateTime startTime = startDate != null ? startDate.atStartOfDay() : null;
            java.time.LocalDateTime endTime = endDate != null ? endDate.plusDays(1).atStartOfDay() : null;

            // 获取所有符合条件的日志（不分页）
            IPage<OperationLogVO> logPage = operationLogService.getOperationLogs(
                    1, 10000, actionType, operatorName, startTime, endTime);

            // 生成CSV内容
            StringBuilder csv = new StringBuilder();
            csv.append("操作时间,操作员,操作类型,操作对象,操作描述,IP地址,执行时间(ms),结果\n");

            for (OperationLogVO log : logPage.getRecords()) {
                csv.append(log.getTime()).append(",");
                csv.append(log.getOperatorName()).append(",");
                csv.append(log.getActionType()).append(",");
                csv.append(log.getTarget()).append(",");
                csv.append(log.getDescription() != null ? log.getDescription().replace(",", "，") : "").append(",");
                csv.append(log.getIpAddress()).append(",");
                csv.append(log.getExecutionTime()).append(",");
                csv.append(log.getResult()).append("\n");
            }

            byte[] csvBytes = csv.toString().getBytes("UTF-8");

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDispositionFormData("attachment",
                    "operation_logs_" + java.time.LocalDate.now() + ".csv");

            return ResponseEntity.ok()
                    .headers(headers)
                    .body(csvBytes);

        } catch (Exception e) {
            log.error("导出操作日志失败", e);
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 获取操作统计数据
     */
    @GetMapping("/operation-logs/statistics")
    public R<Map<String, Object>> getOperationStatistics(
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") java.time.LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") java.time.LocalDate endDate) {

        try {
            java.time.LocalDateTime startTime = startDate != null ? startDate.atStartOfDay() :
                    java.time.LocalDateTime.now().minusDays(7);
            java.time.LocalDateTime endTime = endDate != null ? endDate.plusDays(1).atStartOfDay() :
                    java.time.LocalDateTime.now();

            List<Map<String, Object>> actionStats = operationLogService.getActionStatistics(startTime, endTime);
            Integer todayCount = operationLogService.getTodayOperationCount();
            Integer failedCount = operationLogService.getFailedOperationCount(startTime);

            Map<String, Object> result = new HashMap<>();
            result.put("actionStatistics", actionStats);
            result.put("todayOperationCount", todayCount);
            result.put("failedOperationCount", failedCount);

            return R.ok(result);
        } catch (Exception e) {
            log.error("获取操作统计数据失败", e);
            return R.fail("获取操作统计数据失败");
        }
    }
}