package com.example.interceptor;

import com.example.config.LoginUserContext;
import com.example.util.HttpStatus;
import com.example.util.JwtUtil;
import com.example.util.R;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
@Component
public class JwtInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws IOException {
        // 从请求头中获取token
        String token = request.getHeader("Authorization");
        
        // 如果token以Bearer 开头，去除这个前缀
        if (token != null && token.startsWith("Bearer ")) {
            token = token.substring(7);
        }
        
        // 验证token
        if (token == null || !JwtUtil.validateToken(token)) {
            response.setContentType("application/json;charset=UTF-8");
            response.getWriter().write(new ObjectMapper().writeValueAsString(R.fail(HttpStatus.UNAUTHORIZED,"未登录或token已过期")));
            return false;
        }
        LoginUserContext.setUserInfo(JwtUtil.getUserInfoFromToken(token));
        return true;
    }
}