package com.example.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.entity.Order;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import java.util.List;

@Mapper
public interface OrderMapper extends BaseMapper<Order> {
    
    /**
     * 查询用户的订单列表
     */
    @Select("SELECT * FROM order_info WHERE user_id = #{userId} ORDER BY create_time DESC")
    List<Order> findByUserId(@Param("userId") Long userId);
    
    /**
     * 查询代取员的订单列表
     */
    @Select("SELECT * FROM order_info WHERE pickup_user_id = #{pickupUserId} ORDER BY create_time DESC")
    List<Order> findByPickupUserId(@Param("pickupUserId") Long pickupUserId);
    
    /**
     * 查询待接单的订单列表
     */
    @Select("SELECT * FROM order_info WHERE order_status = 1 ORDER BY create_time ASC")
    List<Order> findPendingOrders();
    
    /**
     * 根据订单号查询订单
     */
    @Select("SELECT * FROM order_info WHERE order_no = #{orderNo}")
    Order findByOrderNo(@Param("orderNo") String orderNo);
}