package com.example.controller;

import com.example.config.LoginUserContext;
import com.example.dto.RechargeDTO;
import com.example.service.WalletService;
import com.example.util.R;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.math.BigDecimal;

/**
 * 钱包控制器
 */
@RestController
@RequestMapping("/wallet")
public class WalletController {

    @Resource
    private WalletService walletService;

    /**
     * 获取用户余额
     */
    @GetMapping("/balance")
    public R<?> getBalance() {
        Long userId = LoginUserContext.getUserInfo().getUserId();
        return R.ok(walletService.getUserBalance(userId));
    }

    /**
     * 获取交易记录
     */
    @GetMapping("/transactions")
    public R<?> getTransactions(@RequestParam(defaultValue = "1") Integer page,
                               @RequestParam(defaultValue = "10") Integer size) {
        Long userId = LoginUserContext.getUserInfo().getUserId();
        return R.ok(walletService.getUserTransactions(userId, page, size));
    }

    /**
     * 充值
     */
    @PostMapping("/recharge")
    public R<?> recharge(@RequestBody @Validated RechargeDTO rechargeDTO) {
        Long userId = LoginUserContext.getUserInfo().getUserId();
        walletService.recharge(userId, rechargeDTO);
        return R.ok("充值成功");
    }

    /**
     * 提现
     */
    @PostMapping("/withdraw")
    public R<?> withdraw(@RequestParam BigDecimal amount) {
        Long userId = LoginUserContext.getUserInfo().getUserId();
        walletService.withdraw(userId, amount);
        return R.ok("提现申请已提交");
    }
}