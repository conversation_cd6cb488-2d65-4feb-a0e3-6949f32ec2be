package com.example.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.example.entity.ExpressStation;
import com.example.entity.Order;
import com.example.entity.SysUser;
import com.example.mapper.ExpressStationMapper;
import com.example.mapper.OrderMapper;
import com.example.mapper.UserMapper;
import com.example.util.R;
import com.example.vo.OrderVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 管理员数据统计控制器
 */
@RestController
@RequestMapping("/admin/statistics")
public class AdminStatisticsController {
    
    @Autowired
    private UserMapper userMapper;
    
    @Autowired
    private OrderMapper orderMapper;
    
    @Autowired
    private ExpressStationMapper expressStationMapper;
    
    /**
     * 获取系统统计数据
     */
    @GetMapping
    public R<Map<String, Object>> getStatistics() {
        Map<String, Object> stats = new HashMap<>();
        
        // 用户统计
        QueryWrapper<SysUser> userQuery = new QueryWrapper<>();
        userQuery.eq("user_type", 1);
        int normalUserCount = userMapper.selectCount(userQuery);
        
        userQuery = new QueryWrapper<>();
        userQuery.eq("user_type", 2);
        int pickupUserCount = userMapper.selectCount(userQuery);
        
        // 订单统计
        int totalOrders = orderMapper.selectCount(null);
        
        // 计算平台收入（假设平台抽成10%）
        QueryWrapper<Order> completedOrderQuery = new QueryWrapper<>();
        completedOrderQuery.eq("order_status", 4); // 假设4是已完成状态
        List<Order> completedOrders = orderMapper.selectList(completedOrderQuery);
        
        BigDecimal totalRevenue = completedOrders.stream()
                .map(order -> order.getFee().multiply(new BigDecimal("0.1")))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        stats.put("totalUsers", normalUserCount);
        stats.put("totalCouriers", pickupUserCount);
        stats.put("totalOrders", totalOrders);
        stats.put("totalRevenue", totalRevenue);
        
        return R.ok(stats);
    }
    
    /**
     * 获取订单趋势数据
     */
    @GetMapping("/order-trend")
    public R<Map<String, Object>> getOrderTrend(@RequestParam String period) {
        Map<String, Object> result = new HashMap<>();
        List<Map<String, Object>> trendData = new ArrayList<>();
        
        LocalDate endDate = LocalDate.now();
        LocalDate startDate;
        
        // 根据周期确定开始日期
        switch (period) {
            case "7d":
                startDate = endDate.minusDays(6);
                break;
            case "30d":
                startDate = endDate.minusDays(29);
                break;
            case "90d":
                startDate = endDate.minusDays(89);
                break;
            default:
                startDate = endDate.minusDays(6);
        }
        
        // 查询时间范围内的订单
        QueryWrapper<Order> query = new QueryWrapper<>();
        query.ge("create_time", startDate.atStartOfDay());
        query.le("create_time", endDate.plusDays(1).atStartOfDay());
        List<Order> orders = orderMapper.selectList(query);
        
        // 按日期分组统计订单数量
        Map<LocalDate, Long> orderCountByDate = orders.stream()
                .collect(Collectors.groupingBy(
                        order -> order.getCreateTime().toLocalDate(),
                        Collectors.counting()
                ));
        
        // 生成趋势数据
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM-dd");
        for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusDays(1)) {
            Map<String, Object> dayData = new HashMap<>();
            dayData.put("date", date.format(formatter));
            dayData.put("count", orderCountByDate.getOrDefault(date, 0L));
            trendData.add(dayData);
        }
        
        result.put("trend", trendData);
        result.put("period", period);
        
        return R.ok(result);
    }
    
    /**
     * 获取热门快递点数据
     */
    @GetMapping("/popular-express-points")
    public R<List<Map<String, Object>>> getPopularExpressPoints() {
        // 查询所有快递站点
        List<ExpressStation> stations = expressStationMapper.selectList(null);
        
        // 查询所有订单
        List<Order> orders = orderMapper.selectList(null);
        
        // 统计每个快递点的订单数量
        Map<Long, Long> orderCountByStation = orders.stream()
                .collect(Collectors.groupingBy(
                        Order::getExpressPointId,
                        Collectors.counting()
                ));
        
        // 找出订单数量最多的站点
        long maxOrders = orderCountByStation.values().stream()
                .max(Long::compare)
                .orElse(1L);
        
        // 构建结果
        List<Map<String, Object>> result = stations.stream()
                .map(station -> {
                    Map<String, Object> pointData = new HashMap<>();
                    pointData.put("name", station.getStationName());
                    pointData.put("location", station.getCampus());
                    
                    long orderCount = orderCountByStation.getOrDefault(station.getStationId(), 0L);
                    pointData.put("orders", orderCount);
                    
                    // 计算百分比
                    int percentage = (int) (orderCount * 100 / maxOrders);
                    pointData.put("percentage", percentage);
                    
                    return pointData;
                })
                .sorted((a, b) -> Long.compare((Long) b.get("orders"), (Long) a.get("orders")))
                .limit(5)
                .collect(Collectors.toList());
        
        return R.ok(result);
    }
    
    /**
     * 获取待处理事项
     */
    @GetMapping("/pending-tasks")
    public R<List<Map<String, Object>>> getPendingTasks() {
        List<Map<String, Object>> tasks = new ArrayList<>();
        
        // 查询待审核的代取员数量
        QueryWrapper<SysUser> pendingCourierQuery = new QueryWrapper<>();
        pendingCourierQuery.eq("user_type", 2).eq("audit_status", 0);
        int pendingCourierCount = userMapper.selectCount(pendingCourierQuery);
        
        if (pendingCourierCount > 0) {
            Map<String, Object> task = new HashMap<>();
            task.put("id", 1);
            task.put("title", "代取员认证审核 (" + pendingCourierCount + ")");
            task.put("time", "刚刚");
            task.put("type", "courier_review");
            tasks.add(task);
        }
        
        // 查询异常订单数量（例如超时未完成的订单）
        QueryWrapper<Order> abnormalOrderQuery = new QueryWrapper<>();
        abnormalOrderQuery.eq("order_status", 2) // 已接单状态
                .lt("create_time", LocalDateTime.now().minusHours(24)); // 24小时前创建的
        int abnormalOrderCount = orderMapper.selectCount(abnormalOrderQuery);
        
        if (abnormalOrderCount > 0) {
            Map<String, Object> task = new HashMap<>();
            task.put("id", 2);
            task.put("title", "异常订单处理 (" + abnormalOrderCount + ")");
            task.put("time", "1小时前");
            task.put("type", "order_issue");
            tasks.add(task);
        }
        
        return R.ok(tasks);
    }
    
    /**
     * 获取系统状态
     */
    @GetMapping("/system-status")
    public R<Map<String, Object>> getSystemStatus() {
        Map<String, Object> status = new HashMap<>();

        // 移除活跃代取员统计
        // 可以添加其他系统状态信息，如服务器负载等
        status.put("serverStatus", "正常");
        status.put("onlineUsers", 0); // 可以根据需要实现在线用户统计

        return R.ok(status);
    }
    
    /**
     * 获取最新订单
     */
    @GetMapping("/recent-orders")
    public R<List<OrderVO>> getRecentOrders() {
        QueryWrapper<Order> query = new QueryWrapper<>();
        query.orderByDesc("create_time");
        query.last("LIMIT 5");
        
        List<Order> orders = orderMapper.selectList(query);
        List<OrderVO> orderVOs = orders.stream().map(order -> {
            OrderVO orderVO = new OrderVO();
            BeanUtils.copyProperties(order, orderVO);
            return orderVO;
        }).collect(Collectors.toList());
        
        return R.ok(orderVOs);
    }
}