package com.example.config;

import com.example.vo.UserInfoVo;

public class LoginUserContext {
    private static final ThreadLocal<UserInfoVo> userIdHolder = new ThreadLocal<>();

    public static void setUserInfo(UserInfoVo vo) {
        userIdHolder.set(vo);
    }

    public static UserInfoVo getUserInfo() {
        return userIdHolder.get();
    }

    public static void clear() {
        userIdHolder.remove();
    }
}