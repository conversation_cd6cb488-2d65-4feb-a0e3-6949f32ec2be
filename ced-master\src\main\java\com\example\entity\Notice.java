package com.example.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 系统公告实体类
 */
@Data
@TableName("sys_notice")
public class Notice {
    
    /**
     * 公告ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 公告标题
     */
    private String title;
    
    /**
     * 公告内容
     */
    private String content;
    
    /**
     * 公告类型（system:系统通知, feature:功能更新, important:重要公告, holiday:节假日通知, security:安全提醒）
     */
    private String type;
    
    /**
     * 目标用户（逗号分隔，如：student,courier）
     */
    private String target;
    
    /**
     * 状态（draft:草稿, published:已发布）
     */
    private String status;
    
    /**
     * 阅读次数
     */
    private Integer readCount;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}