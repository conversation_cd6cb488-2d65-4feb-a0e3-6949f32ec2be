package com.example.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 快递站点实体类 - 对应数据库express_station表
 */
@Data
@TableName("express_station")
public class ExpressStation {
    @TableId(type = IdType.AUTO)
    @TableField("station_id")
    private Long stationId;
    
    @TableField("station_name")
    private String stationName; // 站点名称
    
    @TableField("station_type")
    private String stationType; // 站点类型：菜鸟驿站、京东站点、顺丰站点等
    
    private String campus; // 所属校区
    private String location; // 具体位置
    
    @TableField("open_time")
    private String openTime; // 营业时间
    
    @TableField("contact_phone")
    private String contactPhone; // 联系电话
    
    private Integer status; // 状态：1-正常营业，0-暂停服务
    
    @TableField("create_time")
    private LocalDateTime createTime;
    
    @TableField("update_time")
    private LocalDateTime updateTime;
}