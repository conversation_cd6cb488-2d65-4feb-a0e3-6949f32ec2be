package com.example.controller;

import com.example.config.LoginUserContext;
import com.example.dto.AddressDTO;
import com.example.entity.Address;
import com.example.entity.ExpressStation;
import com.example.service.AddressService;
import com.example.service.ExpressStationService;
import com.example.util.R;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 快递相关控制器
 */
@RestController
@RequestMapping("/express")
public class ExpressController {
    
    @Autowired
    private ExpressStationService expressStationService;
    
    @Autowired
    private AddressService addressService;
    
    /**
     * 查询快递站点列表
     */
    @GetMapping("/stations")
    public R<List<ExpressStation>> getStations() {
        return expressStationService.getAllStations();
    }
    
    /**
     * 根据校区查询快递站点
     */
    @GetMapping("/stations/campus/{campus}")
    public R<List<ExpressStation>> getStationsByCampus(@PathVariable String campus) {
        return expressStationService.getStationsByCampus(campus);
    }
    
    /**
     * 查询站点详情
     */
    @GetMapping("/stations/{stationId}")
    public R<ExpressStation> getStationDetail(@PathVariable Long stationId) {
        return expressStationService.getStationDetail(stationId);
    }
    
    /**
     * 查询用户地址列表
     */
    @GetMapping("/addresses")
    public R<List<Address>> getUserAddresses() {
        Long userId = LoginUserContext.getUserInfo().getUserId();
        return addressService.getUserAddresses(userId);
    }
    
    /**
     * 添加地址
     */
    @PostMapping("/addresses")
    public R<String> addAddress(@Valid @RequestBody AddressDTO addressDTO) {
        Long userId = LoginUserContext.getUserInfo().getUserId();
        return addressService.addAddress(userId, addressDTO);
    }
    
    /**
     * 更新地址
     */
    @PutMapping("/addresses/{addressId}")
    public R<String> updateAddress(@PathVariable Long addressId, @Valid @RequestBody AddressDTO addressDTO) {
        Long userId = LoginUserContext.getUserInfo().getUserId();
        return addressService.updateAddress(userId, addressId, addressDTO);
    }
    
    /**
     * 删除地址
     */
    @DeleteMapping("/addresses/{addressId}")
    public R<String> deleteAddress(@PathVariable Long addressId) {
        Long userId = LoginUserContext.getUserInfo().getUserId();
        return addressService.deleteAddress(userId, addressId);
    }
    
    /**
     * 设置默认地址
     */
    @PutMapping("/addresses/{addressId}/default")
    public R<String> setDefaultAddress(@PathVariable Long addressId) {
        Long userId = LoginUserContext.getUserInfo().getUserId();
        return addressService.setDefaultAddress(userId, addressId);
    }
}