package com.example.controller;

import com.example.config.LoginUserContext;
import com.example.service.NotificationService;
import com.example.util.R;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 通知控制器
 */
@RestController
@RequestMapping("/notification")
public class NotificationController {

    @Resource
    private NotificationService notificationService;

    /**
     * 获取用户通知列表
     */
    @GetMapping("/list")
    public R<?> getNotifications(@RequestParam(defaultValue = "1") Integer page,
                                @RequestParam(defaultValue = "10") Integer size,
                                @RequestParam(required = false) Integer noticeType) {
        Long userId = LoginUserContext.getUserInfo().getUserId();
        return R.ok(notificationService.getUserNotifications(userId, page, size, noticeType));
    }

    /**
     * 获取未读通知数量
     */
    @GetMapping("/unread-count")
    public R<?> getUnreadCount() {
        Long userId = LoginUserContext.getUserInfo().getUserId();
        return R.ok(notificationService.getUnreadCount(userId));
    }

    /**
     * 标记通知为已读
     */
    @PutMapping("/{noticeId}/read")
    public R<?> markAsRead(@PathVariable Long noticeId) {
        Long userId = LoginUserContext.getUserInfo().getUserId();
        notificationService.markAsRead(userId, noticeId);
        return R.ok("标记成功");
    }

    /**
     * 标记所有通知为已读
     */
    @PutMapping("/read-all")
    public R<?> markAllAsRead() {
        Long userId = LoginUserContext.getUserInfo().getUserId();
        notificationService.markAllAsRead(userId);
        return R.ok("全部标记成功");
    }

    /**
     * 删除通知
     */
    @DeleteMapping("/{noticeId}")
    public R<?> deleteNotification(@PathVariable Long noticeId) {
        Long userId = LoginUserContext.getUserInfo().getUserId();
        notificationService.deleteNotification(userId, noticeId);
        return R.ok("删除成功");
    }

    /**
     * 批量标记通知为已读
     */
    @PutMapping("/batch-read")
    public R<?> markMultipleAsRead(@RequestBody List<Long> noticeIds) {
        Long userId = LoginUserContext.getUserInfo().getUserId();
        notificationService.markMultipleAsRead(userId, noticeIds);
        return R.ok("批量标记成功");
    }

    /**
     * 批量删除通知
     */
    @DeleteMapping("/batch-delete")
    public R<?> deleteMultipleNotifications(@RequestBody List<Long> noticeIds) {
        Long userId = LoginUserContext.getUserInfo().getUserId();
        notificationService.deleteMultipleNotifications(userId, noticeIds);
        return R.ok("批量删除成功");
    }

    /**
     * 获取通知详情
     */
    @GetMapping("/{noticeId}")
    public R<?> getNotificationDetail(@PathVariable Long noticeId) {
        Long userId = LoginUserContext.getUserInfo().getUserId();
        return R.ok(notificationService.getNotificationDetail(userId, noticeId));
    }
}