package com.example.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.entity.BaseEntity;
import com.example.entity.Order;
import com.example.entity.SysUser;
import com.example.enums.Status;
import com.example.mapper.OrderMapper;
import com.example.mapper.UserMapper;
import com.example.util.R;
import com.example.vo.CourierDetailVO;
import com.example.vo.CourierVO;
import com.example.vo.OrderVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 管理员代取员管理控制器
 */
@RestController
@RequestMapping("/admin/couriers")
public class AdminCourierController {
    
    @Autowired
    private UserMapper userMapper;
    
    @Autowired
    private OrderMapper orderMapper;
    
    /**
     * 获取代取员列表（分页）
     */
    @GetMapping
    public R<Map<String, Object>> getCouriers(
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) Float minRating,
            @RequestParam(required = false) Float maxRating,
            @RequestParam(required = false) Integer minOrderCount,
            @RequestParam(required = false) Integer maxOrderCount,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        
        Page<SysUser> page = new Page<>(current, size);
        QueryWrapper<SysUser> query = new QueryWrapper<>();
        
        // 只查询代取员
        query.eq("user_type", 2);
        
        // 关键词搜索
        if (keyword != null && !keyword.isEmpty()) {
            query.and(wrapper -> wrapper
                    .like("real_name", keyword)
                    .or()
                    .like("student_id", keyword)
                    .or()
                    .like("phone", keyword));
        }
        
        // 评分筛选
        if (minRating != null) {
            query.ge("rating", minRating);
        }
        if (maxRating != null) {
            query.le("rating", maxRating);
        }
        
        // 订单数量筛选
        if (minOrderCount != null) {
            query.ge("order_count", minOrderCount);
        }
        if (maxOrderCount != null) {
            query.le("order_count", maxOrderCount);
        }
        
        // 日期筛选
        if (startDate != null && !startDate.isEmpty()) {
            query.ge("create_time", LocalDate.parse(startDate).atStartOfDay());
        }
        if (endDate != null && !endDate.isEmpty()) {
            query.le("create_time", LocalDate.parse(endDate).plusDays(1).atStartOfDay());
        }
        
        // 按注册时间降序排序
        query.orderByDesc("create_time");
        
        IPage<SysUser> userPage = userMapper.selectPage(page, query);
        List<CourierVO> courierVOs = userPage.getRecords().stream().map(user -> {
            CourierVO courierVO = new CourierVO();
            BeanUtils.copyProperties(user, courierVO);
            courierVO.setAuditStatusText(getAuditStatusText(user.getAuditStatus()));
            return courierVO;
        }).collect(Collectors.toList());
        
        Map<String, Object> result = new HashMap<>();
        result.put("records", courierVOs);
        result.put("total", userPage.getTotal());
        result.put("pages", userPage.getPages());
        result.put("current", userPage.getCurrent());
        result.put("size", userPage.getSize());
        
        return R.ok(result);
    }
    
    /**
     * 获取待审核的代取员列表
     */
    @GetMapping("/pending")
    public R<Map<String, Object>> getPendingCouriers(
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "20") Integer size) {
        
        Page<SysUser> page = new Page<>(current, size);
        QueryWrapper<SysUser> query = new QueryWrapper<>();
        query.eq("user_type", 2).eq("audit_status", 0);
        query.orderByDesc("create_time");
        
        IPage<SysUser> userPage = userMapper.selectPage(page, query);
        List<CourierVO> courierVOs = userPage.getRecords().stream().map(user -> {
            CourierVO courierVO = new CourierVO();
            BeanUtils.copyProperties(user, courierVO);
            courierVO.setAuditStatusText(getAuditStatusText(user.getAuditStatus()));
            return courierVO;
        }).collect(Collectors.toList());
        
        Map<String, Object> result = new HashMap<>();
        result.put("records", courierVOs);
        result.put("total", userPage.getTotal());
        result.put("pages", userPage.getPages());
        result.put("current", userPage.getCurrent());
        result.put("size", userPage.getSize());
        
        return R.ok(result);
    }
    
    /**
     * 获取已通过审核的代取员列表
     */
    @GetMapping("/approved")
    public R<Map<String, Object>> getApprovedCouriers(
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "20") Integer size) {
        
        Page<SysUser> page = new Page<>(current, size);
        QueryWrapper<SysUser> query = new QueryWrapper<>();
        query.eq("user_type", 2).eq("audit_status", 1);
        query.orderByDesc("create_time");
        
        IPage<SysUser> userPage = userMapper.selectPage(page, query);
        List<CourierVO> courierVOs = userPage.getRecords().stream().map(user -> {
            CourierVO courierVO = new CourierVO();
            BeanUtils.copyProperties(user, courierVO);
            courierVO.setAuditStatusText(getAuditStatusText(user.getAuditStatus()));
            return courierVO;
        }).collect(Collectors.toList());
        
        Map<String, Object> result = new HashMap<>();
        result.put("records", courierVOs);
        result.put("total", userPage.getTotal());
        result.put("pages", userPage.getPages());
        result.put("current", userPage.getCurrent());
        result.put("size", userPage.getSize());
        
        return R.ok(result);
    }
    
    /**
     * 获取已拒绝的代取员列表
     */
    @GetMapping("/rejected")
    public R<Map<String, Object>> getRejectedCouriers(
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "20") Integer size) {
        
        Page<SysUser> page = new Page<>(current, size);
        QueryWrapper<SysUser> query = new QueryWrapper<>();
        query.eq("user_type", 2).eq("audit_status", 2);
        query.orderByDesc("create_time");
        
        IPage<SysUser> userPage = userMapper.selectPage(page, query);
        List<CourierVO> courierVOs = userPage.getRecords().stream().map(user -> {
            CourierVO courierVO = new CourierVO();
            BeanUtils.copyProperties(user, courierVO);
            courierVO.setAuditStatusText(getAuditStatusText(user.getAuditStatus()));
            return courierVO;
        }).collect(Collectors.toList());
        
        Map<String, Object> result = new HashMap<>();
        result.put("records", courierVOs);
        result.put("total", userPage.getTotal());
        result.put("pages", userPage.getPages());
        result.put("current", userPage.getCurrent());
        result.put("size", userPage.getSize());
        
        return R.ok(result);
    }
    
    /**
     * 获取代取员详情
     */
    @GetMapping("/{courierId}/detail")
    public R<CourierDetailVO> getCourierDetail(@PathVariable Long courierId) {
        SysUser user = userMapper.selectById(courierId);
        /*if (user == null || user.getUserType() != 2) {
            return R.fail("代取员不存在");
        }*/
        
        CourierDetailVO detailVO = new CourierDetailVO();
        BeanUtils.copyProperties(user, detailVO);
        detailVO.setAuditStatusText(getAuditStatusText(user.getAuditStatus()));
        
        // 获取代取员最近的订单
        QueryWrapper<Order> orderQuery = new QueryWrapper<>();
        orderQuery.eq("courier_id", courierId);
        orderQuery.orderByDesc("create_time");
        orderQuery.last("LIMIT 5");
        List<Order> recentOrders = orderMapper.selectList(orderQuery);
        
        List<OrderVO> orderVOs = recentOrders.stream().map(order -> {
            OrderVO orderVO = new OrderVO();
            BeanUtils.copyProperties(order, orderVO);
            return orderVO;
        }).collect(Collectors.toList());
        
        detailVO.setRecentOrders(orderVOs);
        
        return R.ok(detailVO);
    }
    
    /**
     * 审核代取员
     */
    @PostMapping("/{courierId}/audit")
    public R<String> auditCourier(
            @PathVariable Long courierId,
            @RequestBody Map<String, Object> params) {

        String status = (String) params.get("status");
        String comment = (String) params.get("comment");
        String auditReason = (String) params.get("auditReason");

        if (status == null || (!status.equals("approved") && !status.equals("rejected"))) {
            return R.fail("审核状态参数错误");
        }

        SysUser user = userMapper.selectById(courierId);
        /*if (user == null || user.getUserType() != 2) {
            return R.fail("代取员不存在");
        }*/

        // 更新审核状态
        user.setAuditStatus(status.equals("approved") ? 1 : 2);
        user.setAuditComment(comment);
        user.setAuditReason(auditReason);
        user.setAuditTime(LocalDateTime.now());
        user.setUpdateTime(LocalDateTime.now());

        // 如果审核通过，设置初始等级和状态
        if (status.equals("approved")) {
            user.setStatus(Status.active); // 启用状态
            user.setCourierLevel(1); // 初级代取员
            user.setRating(new BigDecimal("5.0")); // 初始评分
            user.setTotalEarnings(new BigDecimal("0"));
            user.setMonthlyEarnings(new BigDecimal("0"));
            user.setCompletedOrders(0);
            user.setCancelledOrders(0);
            user.setComplaints(0);
        }

        userMapper.updateById(user);

        String result = status.equals("approved") ? "审核通过" : "审核拒绝";
        return R.ok(result);
    }
    
    /**
     * 更新代取员状态（启用/禁用）
     */
    @PostMapping("/{courierId}/status")
    public R<String> updateCourierStatus(
            @PathVariable Long courierId,
            @RequestParam String status) {
        
        if (!status.equals("approved") && !status.equals("suspended")) {
            return R.fail("状态参数错误");
        }
        
        SysUser user = userMapper.selectById(courierId);
        /*if (user == null || user.getUserType() != 2) {
            return R.fail("代取员不存在");
        }*/
        
        // 更新状态
        user.setUpdateTime(LocalDateTime.now());
        userMapper.updateById(user);
        
        String result = status.equals("approved") ? "代取员已激活" : "代取员已暂停";
        return R.ok(result);
    }
    
    private String getStatusText(Integer status) {
        return status == 1 ? "正常" : "禁用";
    }
    
    private String getAuditStatusText(Integer auditStatus) {
        switch (auditStatus) {
            case 0: return "待审核";
            case 1: return "审核通过";
            case 2: return "审核拒绝";
            default: return "未知";
        }
    }

    /**
     * 获取代取员绩效统计
     */
    @GetMapping("/{courierId}/performance")
    public R<Map<String, Object>> getCourierPerformance(@PathVariable Long courierId) {
        SysUser user = userMapper.selectById(courierId);
        /*if (user == null || user.getUserType() != 2) {
            return R.fail("代取员不存在");
        }*/

        Map<String, Object> performance = new HashMap<>();

        // 基本统计
        performance.put("totalOrders", user.getOrderCount() != null ? user.getOrderCount() : 0);
        performance.put("completedOrders", user.getCompletedOrders() != null ? user.getCompletedOrders() : 0);
        performance.put("cancelledOrders", user.getCancelledOrders() != null ? user.getCancelledOrders() : 0);
        performance.put("complaints", user.getComplaints() != null ? user.getComplaints() : 0);
        performance.put("rating", user.getRating() != null ? user.getRating() : new BigDecimal("5.0"));
        performance.put("totalEarnings", user.getTotalEarnings() != null ? user.getTotalEarnings() : new BigDecimal("0"));
        performance.put("monthlyEarnings", user.getMonthlyEarnings() != null ? user.getMonthlyEarnings() : new BigDecimal("0"));

        // 计算完成率
        int totalOrders = user.getOrderCount() != null ? user.getOrderCount() : 0;
        int completedOrders = user.getCompletedOrders() != null ? user.getCompletedOrders() : 0;
        double completionRate = totalOrders > 0 ? (double) completedOrders / totalOrders * 100 : 0;
        performance.put("completionRate", Math.round(completionRate * 100.0) / 100.0);

        // 计算投诉率
        int complaints = user.getComplaints() != null ? user.getComplaints() : 0;
        double complaintRate = completedOrders > 0 ? (double) complaints / completedOrders * 100 : 0;
        performance.put("complaintRate", Math.round(complaintRate * 100.0) / 100.0);

        return R.ok(performance);
    }

    /**
     * 更新代取员等级
     */
    @PostMapping("/{courierId}/level")
    public R<String> updateCourierLevel(
            @PathVariable Long courierId,
            @RequestParam Integer level) {

        if (level < 1 || level > 4) {
            return R.fail("等级参数错误，等级范围为1-4");
        }

        SysUser user = userMapper.selectById(courierId);
        /*if (user == null || user.getUserType() != 2) {
            return R.fail("代取员不存在");
        }*/

        user.setCourierLevel(level);
        user.setUpdateTime(LocalDateTime.now());
        userMapper.updateById(user);

        String[] levelNames = {"", "初级代取员", "中级代取员", "高级代取员", "专家代取员"};
        return R.ok("等级已更新为：" + levelNames[level]);
    }
}