package com.example.dto;

import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * 用户信息更新DTO
 */
@Data
public class UserUpdateDTO {
    
    @NotBlank(message = "真实姓名不能为空")
    @Size(max = 50, message = "真实姓名长度不能超过50个字符")
    private String realName;
    
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;
    
    @Size(max = 100, message = "支付宝账号长度不能超过100个字符")
    private String alipayAccount;
    
    @Size(max = 100, message = "微信账号长度不能超过100个字符")
    private String wechatAccount;
    
    @Size(max = 100, message = "邮箱长度不能超过100个字符")
    private String email;
    
    private String avatar;
}